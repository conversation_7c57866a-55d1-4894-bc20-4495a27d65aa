# This workflow will build a .NET project
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-net

name: iOS & Android Build & Deploy

concurrency:
  group: build-${{ github.ref }}
  cancel-in-progress: true

permissions:
  contents: read
  issues: write

on:
  workflow_dispatch:  # Manual trigger only
    inputs:
      platform:
        description: 'Platform?'
        required: true
        type: choice
        options:
          - 'iOS & Android'
          - 'iOS'
          - 'Android'
        default: 'iOS & Android'

jobs:
  # Performance tracking job to record workflow start time and generate unified version
  workflow-start:
    name: Record Workflow Start Time & Generate Version
    runs-on: ubicloud-standard-2
    outputs:
      start-time: ${{ steps.start-time.outputs.start-time }}
      version-code: ${{ steps.version.outputs.version_code }}
      version-name: ${{ steps.version.outputs.version_name }}
      assembly-version: ${{ steps.version.outputs.assembly_version }}
      android_version_code: ${{ steps.version.outputs.android_version_code }}
    steps:
    - name: Record workflow start time
      id: start-time
      run: |
        START_TIME=$(date +%s)
        echo "start-time=$START_TIME" >> $GITHUB_OUTPUT
        echo "Workflow started at: $(date -d @$START_TIME)"

    - name: Generate unified version for iOS & Android
      id: version
      run: |
        # Get current date components for clean versioning
        YEAR=$(date +%Y)
        MONTH=$(date +%m)
        DAY=$(date +%d)
        BUILD_NUMBER=$GITHUB_RUN_NUMBER

        # Create clean 3-part version: 3.YYMM.DDXX (XX = last 2 digits of run)
        # All components stay under 65535 for .NET compatibility
        YY=$(echo $YEAR | tail -c 3)  # Last 2 digits of year (25 for 2025)
        YYMM="${YY}$(printf "%02d" $((10#$MONTH)))"  # YYMM format (2506 for June 2025)
        RUN_LAST_TWO=$(printf "%02d" $((BUILD_NUMBER % 100)))  # Last 2 digits of run (12 for run 112)
        DD_XX="$(printf "%02d" $((10#$DAY)))${RUN_LAST_TWO}"  # DDXX (1612 for 16th day, run 112)

        # Single clean 3-part version for everything
        CLEAN_VERSION="3.${YYMM}.${DD_XX}"

        # Android version code: use YYYYMMDDXX format (XX = last 2 digits of run number)
        # This naturally increments above previous format while staying under 2,147,483,647
        # Example: run 112 → XX = 12, result: 2025061612 > previous 2025060877
        # Reuse the same RUN_LAST_TWO from version generation for consistency
        ANDROID_VERSION_CODE="${YEAR}$(printf "%02d%02d" $((10#$MONTH)) $((10#$DAY)))${RUN_LAST_TWO}"

        echo "version_code=$CLEAN_VERSION" >> $GITHUB_OUTPUT
        echo "version_name=$CLEAN_VERSION" >> $GITHUB_OUTPUT
        echo "assembly_version=$CLEAN_VERSION" >> $GITHUB_OUTPUT
        echo "android_version_code=$ANDROID_VERSION_CODE" >> $GITHUB_OUTPUT

        echo "Using clean 3-part version format:"
        echo "  Version: $CLEAN_VERSION (3.YYMM.DDXX where XX = last 2 digits of run)"
        echo "  Android Version Code: $ANDROID_VERSION_CODE (YYYYMMDDXX)"
        echo "  Works for: iOS, Android, .NET Assembly (all components < 65535)"
        echo "  Example: 3.2506.1212 = June 12th, 2025, run #112 → XX=12"
        echo "  Android Code: 2025061212 = June 12th, 2025, run #112 → XX=12"
        echo "  Both version and Android code end with same 4 digits: 1212"

  # Wake up Scaleway Mac mini before iOS build
  wake-scaleway-runner:
    name: Wake Up Scaleway Mac Runner
    if: github.event.inputs.platform == 'iOS & Android' || github.event.inputs.platform == 'iOS'
    runs-on: ubicloud-standard-2
    needs: workflow-start
    outputs:
      runner-status: ${{ steps.check-runner.outputs.runner-status }}
    steps:
    - name: Wake up Scaleway Mac mini
      env:
        SCW_ACCESS_KEY: ${{ secrets.SCW_ACCESS_KEY }}
        SCW_SECRET_KEY: ${{ secrets.SCW_SECRET_KEY }}
        SCW_DEFAULT_PROJECT_ID: ${{ secrets.SCW_DEFAULT_PROJECT_ID }}
        SCW_DEFAULT_ORGANIZATION_ID: ${{ secrets.SCW_DEFAULT_ORGANIZATION_ID }}
        SCALEWAY_SERVER_ID: ${{ secrets.SCALEWAY_SERVER_ID }}
      run: |
        echo "🚀 Attempting to wake up Scaleway Mac mini..."

        # Validate required secrets
        if [ -z "$SCW_ACCESS_KEY" ] || [ -z "$SCW_SECRET_KEY" ] || [ -z "$SCW_DEFAULT_PROJECT_ID" ] || [ -z "$SCALEWAY_SERVER_ID" ]; then
          echo "❌ Missing required Scaleway secrets. Please configure:"
          echo "   - SCW_ACCESS_KEY"
          echo "   - SCW_SECRET_KEY"
          echo "   - SCW_DEFAULT_PROJECT_ID"
          echo "   - SCW_DEFAULT_ORGANIZATION_ID (optional)"
          echo "   - SCALEWAY_SERVER_ID"
          exit 1
        fi

        # Install Scaleway CLI
        echo "📦 Installing Scaleway CLI..."
        curl -s https://raw.githubusercontent.com/scaleway/scaleway-cli/master/scripts/get.sh | sh
        export PATH="$HOME/.local/bin:$PATH"

        # Verify CLI installation
        if ! command -v scw &> /dev/null; then
          echo "❌ Failed to install Scaleway CLI"
          exit 1
        fi

        # Scaleway CLI will automatically use SCW_* environment variables
        echo "🔧 Scaleway CLI configured via environment variables"
        echo "✅ Using SCW_ACCESS_KEY, SCW_SECRET_KEY, SCW_DEFAULT_PROJECT_ID"

        # Verify CLI can authenticate (without exposing secrets)
        echo "🔍 Verifying Scaleway CLI authentication..."
        if ! scw account project list >/dev/null 2>&1; then
          echo "❌ Failed to authenticate with Scaleway API"
          echo "ℹ️ Check your SCW_ACCESS_KEY, SCW_SECRET_KEY, and SCW_DEFAULT_PROJECT_ID"
          exit 1
        fi
        echo "✅ Scaleway CLI authentication successful"

        # Check current server status
        echo "🔍 Checking current server status..."
        if ! SERVER_STATUS=$(scw instance server get "$SCALEWAY_SERVER_ID" -o json | jq -r '.state' 2>/dev/null); then
          echo "❌ Failed to get server status. Check server ID and credentials."
          echo "ℹ️ Make sure SCALEWAY_SERVER_ID is set to your Mac mini's server ID"
          exit 1
        fi

        echo "📊 Current server status: $SERVER_STATUS"

        # Start server if needed
        case "$SERVER_STATUS" in
          "stopped"|"stopped in place")
            echo "🔄 Starting Scaleway Mac mini server..."
            if scw instance server start "$SCALEWAY_SERVER_ID"; then
              echo "✅ Start command sent successfully"
            else
              echo "❌ Failed to start server"
              exit 1
            fi
            ;;
          "running")
            echo "✅ Server is already running"
            ;;
          "starting")
            echo "🔄 Server is already starting"
            ;;
          *)
            echo "⚠️ Server is in unexpected state: $SERVER_STATUS"
            echo "ℹ️ Proceeding anyway..."
            ;;
        esac

    - name: Wait for server to be running
      env:
        SCALEWAY_SERVER_ID: ${{ secrets.SCALEWAY_SERVER_ID }}
      run: |
        echo "⏳ Waiting for server to be fully running..."

        # Wait up to 4 minutes for server to start (reasonable for Mac mini)
        TIMEOUT=240
        ELAPSED=0
        INTERVAL=15

        while [ $ELAPSED -lt $TIMEOUT ]; do
          if SERVER_STATUS=$(scw instance server get "$SCALEWAY_SERVER_ID" -o json | jq -r '.state' 2>/dev/null); then
            echo "📊 Server status: $SERVER_STATUS (${ELAPSED}s elapsed)"

            if [ "$SERVER_STATUS" = "running" ]; then
              echo "✅ Server is now running!"
              break
            fi
          else
            echo "⚠️ Failed to check server status, retrying..."
          fi

          sleep $INTERVAL
          ELAPSED=$((ELAPSED + INTERVAL))
        done

        if [ $ELAPSED -ge $TIMEOUT ]; then
          echo "❌ Timeout waiting for server to start after ${TIMEOUT}s"
          echo "ℹ️ Server may still be starting. GitHub Actions will wait for runner."
          # Don't exit 1 here - let GitHub Actions handle runner availability
        fi

    - name: Wait for GitHub Actions runner to come online
      id: check-runner
      run: |
        echo "⏳ Waiting for GitHub Actions runner to come online..."
        echo "ℹ️ The runner service should auto-start when the Mac mini boots"

        # Give the runner service time to start and register with GitHub
        echo "⏱️ Waiting 90 seconds for runner service to start and register..."
        sleep 90

        echo "✅ Wake-up process completed"
        echo "ℹ️ GitHub Actions will now wait for the runner to become available"
        echo "runner-status=wake-completed" >> $GITHUB_OUTPUT

  build-ios:
    name: iOS Build (Scaleway)
    if: github.event.inputs.platform == 'iOS & Android' || github.event.inputs.platform == 'iOS'
    runs-on: [self-hosted, macos]
    needs: [workflow-start, wake-scaleway-runner]
    outputs:
      ios-start-time: ${{ steps.ios-start-time.outputs.ios-start-time }}
      build-start-time: ${{ steps.build_ios.outputs.build-start-time }}
      build-end-time: ${{ steps.build_ios.outputs.build-end-time }}
      build-duration: ${{ steps.build_ios.outputs.build-duration }}
      restore-duration: ${{ steps.restore_deps.outputs.restore-duration }}
      dotnet-cache-hit: ${{ steps.dotnet-cache.outputs.cache-hit }}
      nuget-cache-hit: ${{ steps.nuget-cache.outputs.cache-hit }}
      ipa-size-mb: ${{ steps.ipa_analysis.outputs.ipa-size-mb }}
      build-success: ${{ steps.build_ios.outputs.build-success }}
      ipa-found: ${{ steps.check_ipa.outputs.ipa-found }}
      testflight-upload-success: ${{ steps.upload_testflight.outputs.upload-success }}
      version-code: ${{ needs.workflow-start.outputs.version-code }}

    steps:
    - name: Record iOS job start time
      id: ios-start-time
      run: |
        IOS_START_TIME=$(date +%s)
        echo "ios-start-time=$IOS_START_TIME" >> $GITHUB_OUTPUT
        echo "iOS build started at: $(date -r $IOS_START_TIME)"
    - uses: actions/checkout@v4

    - name: Install Sentry CLI
      run: |
          brew install getsentry/tools/sentry-cli
          echo "Sentry CLI installed successfully"

    - name: Verify SENTRY_AUTH_TOKEN is Set
      run: echo "SENTRY_AUTH_TOKEN=${{ secrets.SENTRY_AUTH_TOKEN }}" | sed 's/./& /g'

    - name: Authenticate with Sentry
      env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
      run: |
          if [ -z "$SENTRY_AUTH_TOKEN" ]; then
            echo "❌ SENTRY_AUTH_TOKEN is not set!"
            exit 1
          else
            echo "✅ SENTRY_AUTH_TOKEN is set, proceeding with authentication..."
          fi
          sentry-cli login --auth-token "$SENTRY_AUTH_TOKEN"
    - name: Cache .NET SDK and MAUI Workloads
      id: dotnet-cache
      uses: actions/cache@v3
      with:
          path: ~/.dotnet
          key: dotnet-${{ runner.os }}-${{ hashFiles('global.json') }}
          restore-keys: |
            dotnet-${{ runner.os }}-

    - name: Setup .NET
      if: steps.dotnet-cache.outputs.cache-hit != 'true'
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: 8.0.x
    - name: Verify .NET SDK Version
      run: dotnet --version

    - name: Install .NET MAUI (Only if not cached)
      if: steps.dotnet-cache.outputs.cache-hit != 'true'
      run: dotnet workload install maui

    - name: Cache NuGet Packages
      id: nuget-cache
      uses: actions/cache@v3
      with:
          path: ~/.nuget/packages
          key: nuget-${{ runner.os }}-${{ hashFiles('**/packages.lock.json') }}
          restore-keys: |
            nuget-${{ runner.os }}-

    - name: Track NuGet Cache Performance
      run: |
        if [ "${{ steps.nuget-cache.outputs.cache-hit }}" = "true" ]; then
          echo "✅ NuGet Cache: HIT - Packages restored from cache"
        else
          echo "❌ NuGet Cache: MISS - Will download packages"
        fi

    - name: Install .NET workloads
      run: |
        echo "🔧 Installing required .NET workloads..."
        dotnet workload install maui
        dotnet workload install ios
        echo "✅ Workloads installed successfully"

    - name: Restore dependencies
      id: restore_deps
      run: |
        # Record restore start time
        RESTORE_START_TIME=$(date +%s)
        echo "restore-start-time=$RESTORE_START_TIME" >> $GITHUB_OUTPUT
        echo "🔄 Starting dependency restoration at: $(date -r $RESTORE_START_TIME)"

        # Restore dependencies
        dotnet restore DrMaxMuscle/DrMaxMuscle.csproj -p:BuildPlatform=ios

        # Record restore end time
        RESTORE_END_TIME=$(date +%s)
        RESTORE_DURATION=$((RESTORE_END_TIME - RESTORE_START_TIME))
        echo "restore-end-time=$RESTORE_END_TIME" >> $GITHUB_OUTPUT
        echo "restore-duration=$RESTORE_DURATION" >> $GITHUB_OUTPUT
        echo "✅ Dependencies restored in: $(($RESTORE_DURATION / 60))m $(($RESTORE_DURATION % 60))s"

    - name: Clean up existing keychain
      run: |
        # Remove existing signing keychain if it exists (for self-hosted runners)
        if security list-keychains | grep -q "signing_temp.keychain"; then
          security delete-keychain signing_temp.keychain
          echo "🧹 Removed existing signing_temp.keychain"
        else
          echo "✅ No existing signing keychain to clean up"
        fi

        # Also check for keychain files in the filesystem
        if [ -f "$HOME/Library/Keychains/signing_temp.keychain-db" ]; then
          rm -f "$HOME/Library/Keychains/signing_temp.keychain-db"
          echo "🧹 Removed signing_temp.keychain-db file"
        fi

        if [ -f "$HOME/Library/Keychains/signing_temp.keychain" ]; then
          rm -f "$HOME/Library/Keychains/signing_temp.keychain"
          echo "🧹 Removed signing_temp.keychain file"
        fi

    - name: Install Apple Intermediate Certificate
      run: |
        # Download and install Apple Intermediate Certificate Authority for complete certificate chain
        echo "📥 Downloading Apple Intermediate Certificate Authority..."
        curl -o AppleWWDRCAG3.cer "https://www.apple.com/certificateauthority/AppleWWDRCAG3.cer"

        # Import intermediate certificate into login keychain (no sudo required)
        echo "🔧 Importing intermediate certificate into login keychain..."
        if security import AppleWWDRCAG3.cer -k login.keychain -T /usr/bin/codesign; then
          echo "✅ Apple Intermediate Certificate Authority imported successfully"
        else
          echo "ℹ️ Certificate already exists in keychain (this is fine)"
        fi

        echo "✅ Apple Intermediate Certificate Authority is available in login keychain"

    - name: Import Code-Signing Certificates
      uses: apple-actions/import-codesign-certs@v3
      with:
        p12-file-base64: "${{ secrets.P12_CERTIFICATE }}"
        p12-password: "${{ secrets.P12_CERTIFICATE_PASSWORD }}"
        keychain-password: ""
        create-keychain: true

    - name: Verify certificate import
      run: |
        echo "🔍 Verifying imported certificates..."
        echo "📋 All certificates in keychain:"
        security find-identity -v
        echo ""
        echo "🔐 Code signing certificates specifically:"
        security find-identity -v -p codesigning
        echo ""
        echo "🔍 Listing all keychains..."
        security list-keychains
        echo ""
        echo "🔍 Checking if certificate import succeeded..."
        CERT_COUNT=$(security find-identity -v -p codesigning | grep -c "Apple Distribution" || echo "0")
        if [ "$CERT_COUNT" = "0" ]; then
          echo "❌ No code signing certificates found! Certificate import may have failed."
          echo "🔍 Checking for any certificates at all..."
          security find-identity -v
          echo "🔍 Checking signing_temp keychain specifically..."
          security find-identity -v -p codesigning signing_temp.keychain
          exit 1
        else
          echo "✅ Found code signing certificates in keychain"
        fi

        echo ""
        echo "🔧 Ensuring keychain is accessible for build process..."
        # Set up keychain search order: signing keychain first, then system keychain
        security list-keychains -d user -s signing_temp.keychain login.keychain
        security unlock-keychain -p "" signing_temp.keychain
        security set-keychain-settings -t 7200 -l signing_temp.keychain
        echo "✅ Keychain configured for build access"

    - name: Setup iOS Provisioning Profile
      run: |
        # Create the provisioning profiles directory if it doesn't exist
        mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles/

        # Decode the base64-encoded provisioning profile from secrets and save it
        echo "${{ secrets.IOS_PROVISIONING_PROFILE_BASE64 }}" | base64 --decode > ~/Library/MobileDevice/Provisioning\ Profiles/profile.mobileprovision

        # Verify the profile was created successfully
        if [ -f ~/Library/MobileDevice/Provisioning\ Profiles/profile.mobileprovision ]; then
          echo "✅ Provisioning profile installed successfully"
        else
          echo "❌ Failed to install provisioning profile"
          exit 1
        fi

    - name: Update iOS Info.plist with simple version
      run: |
        # Update iOS Info.plist with simple 3-part version
        CLEAN_VERSION="${{ needs.workflow-start.outputs.version-code }}"

        echo "Updating iOS Info.plist with simple version:"
        echo "Version: $CLEAN_VERSION (3.YYYY.MMDDRunNumber)"

        # Update the main iOS Info.plist
        INFO_PLIST_PATH="DrMaxMuscle/Platforms/iOS/Info.plist"
        if [ -f "$INFO_PLIST_PATH" ]; then
          echo "Found Info.plist at: $INFO_PLIST_PATH"

          # Show current content before update
          echo "Current version content:"
          grep -A1 "CFBundleShortVersionString\|CFBundleVersion" "$INFO_PLIST_PATH"

          # Create backup
          cp "$INFO_PLIST_PATH" "${INFO_PLIST_PATH}.bak"

          # Update both CFBundleShortVersionString and CFBundleVersion with same 3-part version
          # Find the line after CFBundleShortVersionString key and replace the string value
          sed -i.tmp '/CFBundleShortVersionString/{n;s|<string>.*</string>|<string>'$CLEAN_VERSION'</string>|;}' "$INFO_PLIST_PATH"
          # Find the line after CFBundleVersion key and replace the string value
          sed -i.tmp '/CFBundleVersion/{n;s|<string>.*</string>|<string>'$CLEAN_VERSION'</string>|;}' "$INFO_PLIST_PATH"

          # Clean up temporary files
          rm -f "${INFO_PLIST_PATH}.tmp"

          echo "✅ Successfully updated $INFO_PLIST_PATH"
          echo "Updated version content:"
          grep -A1 "CFBundleShortVersionString\|CFBundleVersion" "$INFO_PLIST_PATH"

          # Verify the update was successful
          if grep -q "$CLEAN_VERSION" "$INFO_PLIST_PATH"; then
            echo "✅ Version update verification successful"
          else
            echo "❌ Version update verification failed!"
            exit 1
          fi
        else
          echo "⚠️ iOS Info.plist not found at $INFO_PLIST_PATH"
          exit 1
        fi

    - name: Build ios
      id: build_ios
      run: |
        # Record build start time
        BUILD_START_TIME=$(date +%s)
        echo "build-start-time=$BUILD_START_TIME" >> $GITHUB_OUTPUT
        echo "iOS build compilation started at: $(date -r $BUILD_START_TIME)"

        # Ensure keychain is accessible for build process (re-configure before build)
        echo "🔧 Re-configuring keychain access for build process..."
        security list-keychains -d user -s signing_temp.keychain login.keychain
        security unlock-keychain -p "" signing_temp.keychain
        security set-keychain-settings -t 7200 -l signing_temp.keychain

        # Verify login keychain has intermediate certificate
        echo "🔍 Verifying intermediate certificate in login keychain..."
        if security find-certificate -c "Apple Worldwide Developer Relations Certification Authority" login.keychain > /dev/null 2>&1; then
          echo "✅ Intermediate certificate found in login keychain"
        else
          echo "⚠️ Intermediate certificate not found in login keychain"
        fi

        # Verify certificates are still accessible
        echo "🔍 Verifying certificates before build..."
        CERT_COUNT=$(security find-identity -v -p codesigning | grep -c "valid identities found" || echo "0")
        if [ "$CERT_COUNT" = "0" ]; then
          echo "❌ No code signing certificates found before build!"
          security find-identity -v
          exit 1
        else
          echo "✅ Code signing certificates confirmed before build"
          security find-identity -v -p codesigning
        fi

        # Build iOS with simple 3-part version
        CLEAN_VERSION="${{ needs.workflow-start.outputs.version-code }}"

        echo "Building iOS with simple 3-part version:"
        echo "  Version: $CLEAN_VERSION (3.YYYY.MMDDRunNumber)"

        # Create a temporary file to capture build output
        BUILD_LOG_FILE="ios_build_output.log"

        # Set explicit codesign environment variables for MSBuild
        export CODESIGN_ALLOCATE="/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/codesign_allocate"

        # Build and capture output with explicit certificate override
        dotnet publish DrMaxMuscle/DrMaxMuscle.csproj -f net8.0-ios -c Release \
          -p:BuildPlatform=ios \
          -p:GenerateIpa=true \
          -p:RuntimeIdentifier=ios-arm64 \
          -p:ApplicationVersion="$CLEAN_VERSION" \
          -p:ApplicationDisplayVersion="$CLEAN_VERSION" \
          -p:AssemblyVersion="$CLEAN_VERSION" \
          -p:FileVersion="$CLEAN_VERSION" \
          -p:CFBundleVersion="$CLEAN_VERSION" \
          -p:CFBundleShortVersionString="$CLEAN_VERSION" \
          -p:CodesignKey="Apple Distribution: Dr. Muscle (7AAXZ47995)" \
          -p:CodesignKeychain="signing_temp.keychain" 2>&1 | tee "$BUILD_LOG_FILE"

        # Record build end time and calculate duration
        BUILD_END_TIME=$(date +%s)
        echo "build-end-time=$BUILD_END_TIME" >> $GITHUB_OUTPUT
        BUILD_DURATION=$((BUILD_END_TIME - BUILD_START_TIME))
        echo "build-duration=$BUILD_DURATION" >> $GITHUB_OUTPUT
        echo "iOS build compilation completed at: $(date -r $BUILD_END_TIME)"
        echo "Build duration: $(($BUILD_DURATION / 60))m $(($BUILD_DURATION % 60))s"

        # Check if build was successful
        BUILD_RESULT=$?
        if [ $BUILD_RESULT -eq 0 ]; then
          echo "build-success=true" >> $GITHUB_OUTPUT
          echo "✅ Build completed successfully"
        else
          echo "build-success=false" >> $GITHUB_OUTPUT
          echo "❌ Build failed with exit code $BUILD_RESULT"

          # Extract error messages from the log
          echo "Extracting error messages from build log..."
          ERROR_MESSAGES=$(grep -A 3 -B 1 "error [A-Z0-9]*:" "$BUILD_LOG_FILE" | head -n 20)

          if [ -n "$ERROR_MESSAGES" ]; then
            # Escape newlines and quotes for GitHub output
            ERROR_MESSAGES_ESCAPED=$(echo "$ERROR_MESSAGES" | sed ':a;N;$!ba;s/\n/\\n/g' | sed 's/"/\\"/g')
            echo "build_errors<<EOF" >> $GITHUB_OUTPUT
            echo "$ERROR_MESSAGES_ESCAPED" >> $GITHUB_OUTPUT
            echo "EOF" >> $GITHUB_OUTPUT

            echo "Found the following errors:"
            echo "$ERROR_MESSAGES"
          else
            echo "No specific error messages found in the build log."
            echo "build_errors=No specific error messages found. Check the full log for details." >> $GITHUB_OUTPUT
          fi
        fi

    - name: Check if IPA file exists
      id: check_ipa
      run: |
        IPA_PATH="DrMaxMuscle/bin/Release/net8.0-ios/ios-arm64/publish/DrMaxMuscle.ipa"
        if [ -f "$IPA_PATH" ]; then
          echo "✅ IPA file exists!"
          echo "ipa-found=true" >> $GITHUB_OUTPUT
          echo "ipa-path=$IPA_PATH" >> $GITHUB_OUTPUT
        else
          echo "❌ IPA file is missing!"
          echo "ipa-found=false" >> $GITHUB_OUTPUT
          exit 1
        fi

    - name: IPA Analysis
      if: steps.check_ipa.outputs.ipa-found == 'true'
      id: ipa_analysis
      run: |
        IPA_PATH="${{ steps.check_ipa.outputs.ipa-path }}"
        echo "🔍 Analyzing IPA file: $IPA_PATH"

        # Record analysis start time
        ANALYSIS_START_TIME=$(date +%s)
        echo "analysis-start-time=$ANALYSIS_START_TIME" >> $GITHUB_OUTPUT

        # 1. Size tracking
        IPA_SIZE=$(stat -f%z "$IPA_PATH")
        IPA_SIZE_MB=$(echo "scale=2; $IPA_SIZE / 1024 / 1024" | bc -l)
        echo "ipa-size-bytes=$IPA_SIZE" >> $GITHUB_OUTPUT
        echo "ipa-size-mb=$IPA_SIZE_MB" >> $GITHUB_OUTPUT
        echo "📦 IPA Size: ${IPA_SIZE_MB} MB (${IPA_SIZE} bytes)"

        # 2. Extract IPA for further analysis
        echo "📱 Extracting IPA for analysis..."
        mkdir -p ipa_contents
        unzip -q "$IPA_PATH" -d ipa_contents/

        # 3. Analyze IPA structure
        echo "📋 Analyzing IPA structure..."

        # Count frameworks and libraries
        FRAMEWORKS_COUNT=$(find ipa_contents/ -name "*.framework" 2>/dev/null | wc -l | tr -d ' ')
        echo "frameworks-count=$FRAMEWORKS_COUNT" >> $GITHUB_OUTPUT
        echo "🔧 Frameworks: $FRAMEWORKS_COUNT"

        # Check for large assets
        LARGE_ASSETS=$(find ipa_contents/ -type f -size +1048576c 2>/dev/null | wc -l | tr -d ' ')
        echo "large-assets-count=$LARGE_ASSETS" >> $GITHUB_OUTPUT
        echo "📁 Large assets (>1MB): $LARGE_ASSETS"

        # Record analysis end time
        ANALYSIS_END_TIME=$(date +%s)
        ANALYSIS_DURATION=$((ANALYSIS_END_TIME - ANALYSIS_START_TIME))
        echo "analysis-duration=$ANALYSIS_DURATION" >> $GITHUB_OUTPUT
        echo "⏱️ Analysis completed in: $(($ANALYSIS_DURATION / 60))m $(($ANALYSIS_DURATION % 60))s"

    - name: Install private API key P8
      env:
        PRIVATE_API_KEY_BASE64: ${{ secrets.APPSTORE_API_PRIVATE_KEY }}
        API_KEY: ${{ secrets.APPSTORE_API_KEY_ID }}
      run: |
        mkdir -p ~/private_keys
        echo -n "$PRIVATE_API_KEY_BASE64" | base64 --decode --output AuthKey_$API_KEY.p8
        cp *.p8 ~/private_keys

    - name: Upload app to TestFlight
      id: upload_testflight
      env:
        API_KEY: ${{ secrets.APPSTORE_API_KEY_ID }}
        API_ISSUER : ${{ secrets.APPSTORE_ISSUER_ID  }}
        IPA_PATH: DrMaxMuscle/bin/Release/net8.0-ios/ios-arm64/publish/DrMaxMuscle.ipa
      run: |
        echo "🚀 Uploading to TestFlight..."
        if xcrun altool --upload-app -f $IPA_PATH -t ios --apiKey $API_KEY --apiIssuer $API_ISSUER; then
          echo "upload-success=true" >> $GITHUB_OUTPUT
          echo "✅ Successfully uploaded to TestFlight"
        else
          echo "upload-success=false" >> $GITHUB_OUTPUT
          echo "❌ Failed to upload to TestFlight"
          exit 1
        fi

    # Output iOS build summary
    - name: Output iOS Build Summary
      if: always()
      run: |
        echo "### 🍎 iOS Build Summary" >> $GITHUB_STEP_SUMMARY
        echo "- **Selected Platform:** ${{ github.event.inputs.platform }}" >> $GITHUB_STEP_SUMMARY

        # Add performance metrics with clean time format
        if [ -n "${{ steps.build_ios.outputs.build-duration }}" ]; then
          BUILD_DURATION="${{ steps.build_ios.outputs.build-duration }}"
          echo "- **Build Duration:** $(($BUILD_DURATION / 60))m $(($BUILD_DURATION % 60))s" >> $GITHUB_STEP_SUMMARY
        fi

        # Add cache performance metrics
        echo "- **Cache Performance:**" >> $GITHUB_STEP_SUMMARY
        if [ "${{ steps.dotnet-cache.outputs.cache-hit }}" = "true" ]; then
          echo "  - .NET SDK: ✅ Cache Hit" >> $GITHUB_STEP_SUMMARY
        else
          echo "  - .NET SDK: ❌ Cache Miss" >> $GITHUB_STEP_SUMMARY
        fi
        if [ "${{ steps.nuget-cache.outputs.cache-hit }}" = "true" ]; then
          echo "  - NuGet: ✅ Cache Hit" >> $GITHUB_STEP_SUMMARY
        else
          echo "  - NuGet: ❌ Cache Miss" >> $GITHUB_STEP_SUMMARY
        fi

        # Add build step breakdown
        if [ -n "${{ steps.restore_deps.outputs.restore-duration }}" ]; then
          RESTORE_DURATION="${{ steps.restore_deps.outputs.restore-duration }}"
          echo "- **Build Step Breakdown:**" >> $GITHUB_STEP_SUMMARY
          echo "  - Dependency Restore: $(($RESTORE_DURATION / 60))m $(($RESTORE_DURATION % 60))s" >> $GITHUB_STEP_SUMMARY
        fi

        # Add code signing information
        echo "- **Code Signing:** ✅ P12 Certificate used" >> $GITHUB_STEP_SUMMARY
        echo "- **Provisioning Profile:** ✅ Securely loaded from GitHub secrets" >> $GITHUB_STEP_SUMMARY

        # Add build status information
        if [ "${{ steps.build_ios.outputs.build-success }}" = "true" ]; then
          echo "- **Build:** ✅ Completed successfully" >> $GITHUB_STEP_SUMMARY
        elif [ "${{ steps.build_ios.outputs.build-success }}" = "false" ]; then
          echo "- **Build:** ❌ Failed" >> $GITHUB_STEP_SUMMARY

          # Add build errors if any
          if [ -n "${{ steps.build_ios.outputs.build_errors }}" ]; then
            echo "  - **Build Errors:**" >> $GITHUB_STEP_SUMMARY
            echo "    ```" >> $GITHUB_STEP_SUMMARY
            echo "${{ steps.build_ios.outputs.build_errors }}" >> $GITHUB_STEP_SUMMARY
            echo "    ```" >> $GITHUB_STEP_SUMMARY
          fi
        else
          echo "- **Build:** ⚠️ Status unknown" >> $GITHUB_STEP_SUMMARY
        fi

        # Add package information if found
        if [ "${{ steps.check_ipa.outputs.ipa-found }}" = "true" ]; then
          echo "- **Package:** ✅ Built successfully" >> $GITHUB_STEP_SUMMARY
          echo "- **Package Path:** \`${{ steps.check_ipa.outputs.ipa-path }}\`" >> $GITHUB_STEP_SUMMARY
          echo "- **Version:** ${{ needs.workflow-start.outputs.version-name }}" >> $GITHUB_STEP_SUMMARY
          echo "- **iOS Target:** iOS 11.0+" >> $GITHUB_STEP_SUMMARY

          # Add IPA analysis results
          if [ -n "${{ steps.ipa_analysis.outputs.ipa-size-mb }}" ]; then
            echo "- **Package Analysis:**" >> $GITHUB_STEP_SUMMARY
            echo "  - IPA Size: ${{ steps.ipa_analysis.outputs.ipa-size-mb }} MB" >> $GITHUB_STEP_SUMMARY
            echo "  - Frameworks: ${{ steps.ipa_analysis.outputs.frameworks-count }}" >> $GITHUB_STEP_SUMMARY
            echo "  - Large Assets (>1MB): ${{ steps.ipa_analysis.outputs.large-assets-count }}" >> $GITHUB_STEP_SUMMARY

            if [ -n "${{ steps.ipa_analysis.outputs.analysis-duration }}" ]; then
              ANALYSIS_DURATION="${{ steps.ipa_analysis.outputs.analysis-duration }}"
              echo "  - Analysis Time: $(($ANALYSIS_DURATION / 60))m $(($ANALYSIS_DURATION % 60))s" >> $GITHUB_STEP_SUMMARY
            fi
          fi

          # Add deployment information
          if [ "${{ steps.upload_testflight.outputs.upload-success }}" = "true" ]; then
            echo "- **Deployment:** ✅ Uploaded to TestFlight" >> $GITHUB_STEP_SUMMARY
            echo "- **Distribution:** TestFlight (Internal Testing)" >> $GITHUB_STEP_SUMMARY
            echo "- **Bundle ID:** com.drmaxmuscle.dr_max_muscle" >> $GITHUB_STEP_SUMMARY
          else
            echo "- **Deployment:** ⚠️ Not uploaded to TestFlight" >> $GITHUB_STEP_SUMMARY
            echo "  - ℹ️ Reason: Upload failed or credentials missing" >> $GITHUB_STEP_SUMMARY
          fi
        elif [ "${{ steps.build_ios.outputs.build-success }}" = "false" ]; then
          echo "- **Package:** ❌ Build failed, no package was generated" >> $GITHUB_STEP_SUMMARY
        else
          echo "- **Package:** ❌ Build process completed but no package was found" >> $GITHUB_STEP_SUMMARY
        fi


  build-android:
    name: Android Build (Linux)
    if: github.event.inputs.platform == 'iOS & Android' || github.event.inputs.platform == 'Android'
    runs-on: ubicloud-standard-2
    timeout-minutes: 40  # Increased timeout for the full build process
    needs: workflow-start
    outputs:
      android-start-time: ${{ steps.android-start-time.outputs.android-start-time }}
      build-start-time: ${{ steps.build_android.outputs.build-start-time }}
      build-end-time: ${{ steps.build_android.outputs.build-end-time }}
      build-duration: ${{ steps.build_android.outputs.build-duration }}
      restore-duration: ${{ steps.restore_deps.outputs.restore-duration }}
      nuget-cache-hit: ${{ steps.cache-nuget.outputs.cache-hit }}
      android-sdk-cache-hit: ${{ steps.cache-android-sdk.outputs.cache-hit }}
      aab-size-mb: ${{ steps.aab_analysis.outputs.aab-size-mb }}
      apk-size-mb: ${{ steps.aab_analysis.outputs.apk-size-mb }}
      native-lib-count: ${{ steps.aab_analysis.outputs.native-lib-count }}
      dex-file-count: ${{ steps.aab_analysis.outputs.dex-file-count }}
      large-assets-count: ${{ steps.aab_analysis.outputs.large-assets-count }}
      security-issues-found: ${{ steps.aab_analysis.outputs.security-issues-found }}
      version-code: ${{ needs.workflow-start.outputs.version-code }}

    steps:
    - name: Record Android job start time
      id: android-start-time
      run: |
        ANDROID_START_TIME=$(date +%s)
        echo "android-start-time=$ANDROID_START_TIME" >> $GITHUB_OUTPUT
        echo "Android build started at: $(date -d @$ANDROID_START_TIME)"

    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Check for keystore file
      run: |
        # Check if publishingdoc.keystore exists in repository root
        if [ -f "./publishingdoc.keystore" ]; then
          echo "✅ Found keystore in repository root: ./publishingdoc.keystore"
          ls -la ./publishingdoc.keystore
        else
          echo "❌ No keystore found in repository root"
        fi

    - name: Install Sentry CLI
      run: |
        curl -sL https://sentry.io/get-cli/ | bash
        echo "Sentry CLI installed successfully"

    - name: Verify SENTRY_AUTH_TOKEN is Set
      run: echo "SENTRY_AUTH_TOKEN=${{ secrets.SENTRY_AUTH_TOKEN }}" | sed 's/./& /g'

    - name: Authenticate with Sentry
      env:
        SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
      run: |
        if [ -z "$SENTRY_AUTH_TOKEN" ]; then
          echo "❌ SENTRY_AUTH_TOKEN is not set!"
          exit 1
        else
          echo "✅ SENTRY_AUTH_TOKEN is set, proceeding with authentication..."
        fi
        sentry-cli login --auth-token "$SENTRY_AUTH_TOKEN"

    - name: Setup .NET 8
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: 8.0.x

    # Optimized caching strategy to prevent hanging during cleanup
    - name: Cache NuGet packages
      id: cache-nuget
      uses: actions/cache@v4
      with:
        path: ~/.nuget/packages
        key: nuget-${{ runner.os }}-${{ hashFiles('**/*.csproj') }}
        restore-keys: |
          nuget-${{ runner.os }}-
        # Prevent cross-OS issues and don't fail on cache miss
        enableCrossOsArchive: false
        fail-on-cache-miss: false
        # Limit cache size with lookup-only
        lookup-only: true

    - name: Track NuGet Cache Performance
      run: |
        if [ "${{ steps.cache-nuget.outputs.cache-hit }}" = "true" ]; then
          echo "✅ NuGet Cache: HIT - Packages restored from cache"
          echo "nuget-cache-hit=true" >> $GITHUB_OUTPUT
        else
          echo "❌ NuGet Cache: MISS - Will download packages"
          echo "nuget-cache-hit=false" >> $GITHUB_OUTPUT
        fi

    # Cache Android SDK components to speed up future builds
    - name: Cache Android SDK components
      id: cache-android-sdk
      uses: actions/cache@v4
      with:
        path: |
          ~/.android/avd
          ~/.android/adb*
          ~/.android/debug.keystore
          ~/.android/repositories.cfg
          ~/.android/sdkmanager*
          /usr/local/lib/android/sdk
        key: android-sdk-${{ runner.os }}-${{ hashFiles('**/*.csproj') }}
        restore-keys: |
          android-sdk-${{ runner.os }}-
        enableCrossOsArchive: false
        fail-on-cache-miss: false

    - name: Track Android SDK Cache Performance
      run: |
        if [ "${{ steps.cache-android-sdk.outputs.cache-hit }}" = "true" ]; then
          echo "✅ Android SDK Cache: HIT - SDK components restored from cache"
          echo "android-sdk-cache-hit=true" >> $GITHUB_OUTPUT
        else
          echo "❌ Android SDK Cache: MISS - Will download SDK components"
          echo "android-sdk-cache-hit=false" >> $GITHUB_OUTPUT
        fi

    - name: Verify .NET SDK Version
      run: dotnet --version

    - name: Install .NET MAUI Workload for Android
      run: dotnet workload install maui-android

    - name: List solution files
      run: find . -name "*.sln" -type f

    # Validate keystore
    - name: Setup and validate keystore
      id: setup_keystore
      run: |
        # Check if the keystore exists in the repository root
        if [ -f "./publishingdoc.keystore" ]; then
          echo "Found keystore file in repository root: ./publishingdoc.keystore"

          # Define keystore passwords - use secrets if available, otherwise try both passwords from project files
          keystorePassword="${{ secrets.KEYSTORE_PASSWORD }}"
          keyPassword="${{ secrets.KEY_PASSWORD }}"
          keyAlias="publishingdoc"
          keystore_valid=false

          # If secrets are not set, try both known passwords from the project files
          if [ -z "$keystorePassword" ]; then
            echo "KEYSTORE_PASSWORD secret not found, will try known passwords"

            # Try the first password from DrMaxMuscle.csproj
            echo "===== KEYSTORE PATH DEBUGGING ====="
            echo "Trying password: db2MicorSystem"
            if keytool -list -v -keystore ./publishingdoc.keystore -storepass "db2MicorSystem" -alias $keyAlias > /dev/null 2>&1; then
              echo "✅ KEYSTORE VALIDATION SUCCESSFUL with password: db2MicorSystem"
              keystorePassword="db2MicorSystem"
              keyPassword="db2MicorSystem"
              keystore_valid=true
            else
              # Try the second password from DrMuscle.Droid.csproj
              echo "Trying password: nmrojvbnpG2B"
              if keytool -list -v -keystore ./publishingdoc.keystore -storepass "nmrojvbnpG2B" -alias $keyAlias > /dev/null 2>&1; then
                echo "✅ KEYSTORE VALIDATION SUCCESSFUL with password: nmrojvbnpG2B"
                keystorePassword="nmrojvbnpG2B"
                keyPassword="nmrojvbnpG2B"
                keystore_valid=true
              else
                echo "❌ KEYSTORE VALIDATION FAILED: Could not open keystore with any known password."
              fi
            fi
            echo "===== END KEYSTORE PATH DEBUGGING ====="
          else
            # Use the provided secret
            echo "Using password from GitHub secret"
            if keytool -list -v -keystore ./publishingdoc.keystore -storepass "$keystorePassword" -alias $keyAlias > /dev/null 2>&1; then
              echo "✅ KEYSTORE VALIDATION SUCCESSFUL with provided secret"
              keystore_valid=true
            else
              echo "❌ KEYSTORE VALIDATION FAILED: Could not open keystore with provided secret."
            fi
          fi

          # Store the working password for later steps
          if [ "$keystore_valid" = true ]; then
            echo "keystore_valid=true" >> $GITHUB_OUTPUT
            echo "keystore_password=$keystorePassword" >> $GITHUB_OUTPUT
            echo "key_password=$keyPassword" >> $GITHUB_OUTPUT
          else
            echo "keystore_valid=false" >> $GITHUB_OUTPUT
          fi
        else
          echo "❌ No keystore found in repository root."
          echo "keystore_valid=false" >> $GITHUB_OUTPUT
        fi

    # Validate Google Play service account
    - name: Validate Google Play Service Account
      id: validate_gplay
      run: |
        # Check if the Google Play service account JSON is available (try standard version first, then CARL version)
        if [ -n "${{ secrets.GOOGLE_SERVICE_JSON }}" ]; then
          echo "Google Play service account JSON found in secrets."
          secretContent="${{ secrets.GOOGLE_SERVICE_JSON }}"
        elif [ -n "${{ secrets.GOOGLE_SERVICE_JSON_CARL }}" ]; then
          echo "Google Play service account JSON (CARL version) found in secrets."
          secretContent="${{ secrets.GOOGLE_SERVICE_JSON_CARL }}"
        else
          echo "❌ SERVICE ACCOUNT VALIDATION FAILED: No Google Play service account JSON found in secrets."
          echo "service_account_valid=false" >> $GITHUB_OUTPUT
          exit 0
        fi

        # Create directory for the service account JSON
        mkdir -p google-play

        # The secret might be base64 encoded, so let's try to handle both cases
        # First, check if it looks like base64 (no curly braces at the beginning)
        if [[ ! "${secretContent:0:1}" == "{" ]]; then
            echo "Secret doesn't appear to be raw JSON. Attempting to decode as base64..."
            # Try to decode as base64
            decodedContent=$(echo "$secretContent" | base64 --decode 2>/dev/null)

            # Check if the decoded content looks like JSON
            if [[ "${decodedContent:0:1}" == "{" ]]; then
                echo "Successfully decoded base64 to JSON."
                secretContent="$decodedContent"
            else
                echo "Warning: Decoded content doesn't look like JSON. Will use original content."
            fi
        fi

        # Write the content to the service account file
        echo "$secretContent" > google-play/service-account.json

        # Verify the JSON file was created and has content
        jsonFileSize=$(stat -c%s "google-play/service-account.json")
        echo "JSON file size: $jsonFileSize bytes"

        # Display the first few characters to help diagnose issues (without revealing sensitive data)
        jsonPreview=$(head -c 20 google-play/service-account.json)
        echo "JSON starts with: $jsonPreview..."

        # Check if the file appears to be valid JSON
        if jq . google-play/service-account.json > /dev/null 2>&1; then
            # Verify required fields
            requiredFields=("type" "project_id" "private_key_id" "private_key" "client_email" "client_id")
            missingFields=()

            for field in "${requiredFields[@]}"; do
                if ! jq -e ".$field" google-play/service-account.json > /dev/null 2>&1; then
                    missingFields+=("$field")
                fi
            done

            if [ ${#missingFields[@]} -gt 0 ]; then
                echo "❌ SERVICE ACCOUNT VALIDATION FAILED: Missing required fields: ${missingFields[*]}"
                echo "service_account_valid=false" >> $GITHUB_OUTPUT
            else
                echo "✅ SERVICE ACCOUNT VALIDATION SUCCESSFUL: JSON is valid and contains all required fields."
                echo "service_account_valid=true" >> $GITHUB_OUTPUT
            fi
        else
            echo "❌ SERVICE ACCOUNT VALIDATION FAILED: JSON is not valid."
            echo "service_account_valid=false" >> $GITHUB_OUTPUT
        fi


    # Check credentials and determine if we can proceed with build
    - name: Check if credentials are valid
      id: check_credentials
      run: |
        # Check if the keystore is valid or at least exists
        if [ "${{ steps.setup_keystore.outputs.keystore_valid }}" = "true" ] || [ -f "./publishingdoc.keystore" ]; then
          echo "build_allowed=true" >> $GITHUB_OUTPUT

          # Only allow deployment to Google Play if both keystore and service account are valid
          if [ "${{ steps.setup_keystore.outputs.keystore_valid }}" = "true" ] && [ "${{ steps.validate_gplay.outputs.service_account_valid }}" = "true" ]; then
            echo "credentials_valid=true" >> $GITHUB_OUTPUT
            echo "✅ All credentials are valid. Build will be deployed to Google Play."
          else
            echo "credentials_valid=false" >> $GITHUB_OUTPUT
            echo "⚠️ Some credentials are invalid. Build will be created but NOT deployed to Google Play."
          fi
        else
          echo "build_allowed=false" >> $GITHUB_OUTPUT
          echo "credentials_valid=false" >> $GITHUB_OUTPUT
          echo "❌ No keystore available. Skipping build and deployment."
        fi

    - name: Set CI environment variable
      if: steps.check_credentials.outputs.build_allowed == 'true'
      run: |
        # Set CI environment variable to true for the entire workflow
        echo "CI=true" >> $GITHUB_ENV
        echo "✅ Set CI environment variable to true"

    - name: Fix case-sensitive icon filename
      if: steps.check_credentials.outputs.build_allowed == 'true'
      run: |
        if [ -f "DrMaxMuscle/Resources/AppIcon/Icon.png" ] && [ ! -f "DrMaxMuscle/Resources/AppIcon/icon.png" ]; then
          echo "Copying Icon.png to icon.png to fix case sensitivity issue"
          cp "DrMaxMuscle/Resources/AppIcon/Icon.png" "DrMaxMuscle/Resources/AppIcon/icon.png"
        fi

    - name: Restore Dependencies
      if: steps.check_credentials.outputs.build_allowed == 'true'
      id: restore_deps
      run: |
        # Record restore start time
        RESTORE_START_TIME=$(date +%s)
        echo "restore-start-time=$RESTORE_START_TIME" >> $GITHUB_OUTPUT
        echo "🔄 Starting dependency restoration at: $(date -d @$RESTORE_START_TIME)"

        # Restore dependencies
        dotnet restore DrMaxMuscle/DrMaxMuscle.csproj -p:BuildPlatform=android -p:TargetFrameworks=net8.0-android

        # Record restore end time
        RESTORE_END_TIME=$(date +%s)
        RESTORE_DURATION=$((RESTORE_END_TIME - RESTORE_START_TIME))
        echo "restore-end-time=$RESTORE_END_TIME" >> $GITHUB_OUTPUT
        echo "restore-duration=$RESTORE_DURATION" >> $GITHUB_OUTPUT
        echo "✅ Dependencies restored in: $(($RESTORE_DURATION / 60))m $(($RESTORE_DURATION % 60))s"

    - name: Install Android Dependencies (If Cache Miss)
      if: steps.check_credentials.outputs.build_allowed == 'true' && steps.cache-android-sdk.outputs.cache-hit != 'true'
      run: |
        sudo apt update
        sudo apt install -y openjdk-17-jdk unzip
        export ANDROID_HOME=$HOME/Android/Sdk
        export PATH=$ANDROID_HOME/emulator:$ANDROID_HOME/tools:$ANDROID_HOME/tools/bin:$ANDROID_HOME/platform-tools:$PATH
        mkdir -p $ANDROID_HOME/cmdline-tools
        cd $ANDROID_HOME/cmdline-tools
        wget https://dl.google.com/android/repository/commandlinetools-linux-9477386_latest.zip -O commandlinetools.zip
        unzip commandlinetools.zip
        mv cmdline-tools latest
        export PATH=$ANDROID_HOME/cmdline-tools/latest/bin:$PATH
        sdkmanager --licenses
        sdkmanager "platform-tools" "platforms;android-34" "build-tools;34.0.0"

    - name: Use unified version from workflow-start
      if: steps.check_credentials.outputs.build_allowed == 'true'
      id: version
      run: |
        # Use the unified version generated in workflow-start job
        VERSION_CODE="${{ needs.workflow-start.outputs.version-code }}"
        VERSION_NAME="${{ needs.workflow-start.outputs.version-name }}"

        echo "version_code=$VERSION_CODE" >> $GITHUB_OUTPUT
        echo "version_name=$VERSION_NAME" >> $GITHUB_OUTPUT
        echo "Using unified version code: $VERSION_CODE"
        echo "Using unified version name: $VERSION_NAME"

    - name: Setup Keystore File
      if: steps.check_credentials.outputs.build_allowed == 'true'
      run: echo "${{ secrets.KEYSTORE_FILE_LINUX }}" | base64 --decode > keystoreFile.keystore

    - name: Verify Keystore File
      if: steps.check_credentials.outputs.build_allowed == 'true'
      run: |
        if [ -f "keystoreFile.keystore" ]; then
          echo "✅ Keystore file exists and is ready for use"
        else
          echo "❌ Keystore file not found!"
          exit 1
        fi

    - name: Update AndroidManifest.xml files with correct version
      if: steps.check_credentials.outputs.build_allowed == 'true'
      run: |
        # Update the version code and name in all AndroidManifest.xml files
        echo "Searching for all AndroidManifest.xml files:"
        MANIFEST_FILES=$(find . -name "AndroidManifest.xml" -type f)

        if [ -z "$MANIFEST_FILES" ]; then
          echo "❌ No AndroidManifest.xml files found!"
          exit 1
        fi

        echo "Found the following AndroidManifest.xml files:"
        echo "$MANIFEST_FILES"

        # Use unified version from workflow-start (no fallback needed)
        VERSION_CODE="${{ needs.workflow-start.outputs.android_version_code }}"
        VERSION_NAME="${{ steps.version.outputs.version_name }}"

        echo "Using Android version code: $VERSION_CODE"
        echo "Using version name: $VERSION_NAME"

        # Update each manifest file
        for MANIFEST_PATH in $MANIFEST_FILES; do
          echo "Processing $MANIFEST_PATH"

          # Display current manifest content
          echo "Current content:"
          cat "$MANIFEST_PATH"

          # Create a backup of the original file
          cp "$MANIFEST_PATH" "${MANIFEST_PATH}.bak"

          # Update version code and name using sed
          sed -i "s/android:versionCode=\"[0-9]*\"/android:versionCode=\"$VERSION_CODE\"/g" "$MANIFEST_PATH"
          sed -i "s/android:versionName=\"[^\"]*\"/android:versionName=\"$VERSION_NAME\"/g" "$MANIFEST_PATH"

          # Update targetSdkVersion to match API level 34
          if grep -q "android:targetSdkVersion" "$MANIFEST_PATH"; then
            # Update existing targetSdkVersion
            sed -i "s/android:targetSdkVersion=\"[0-9]*\"/android:targetSdkVersion=\"34\"/g" "$MANIFEST_PATH"
            echo "Updated targetSdkVersion to 34"
          else
            # If targetSdkVersion doesn't exist, check if <uses-sdk> tag exists
            if grep -q "<uses-sdk" "$MANIFEST_PATH"; then
              # Add targetSdkVersion to existing <uses-sdk> tag
              sed -i "s/<uses-sdk/<uses-sdk android:targetSdkVersion=\"34\"/g" "$MANIFEST_PATH"
              echo "Added targetSdkVersion=34 to existing <uses-sdk> tag"
            else
              # If <uses-sdk> tag doesn't exist, add it before </manifest>
              sed -i "s|</manifest>|    <uses-sdk android:targetSdkVersion=\"34\" />\n</manifest>|g" "$MANIFEST_PATH"
              echo "Added new <uses-sdk> tag with targetSdkVersion=34"
            fi
          fi

          echo "Updated content:"
          cat "$MANIFEST_PATH"

          echo "✅ Successfully updated $MANIFEST_PATH"
        done

        # Also update the ApplicationDisplayVersion and ApplicationVersion in the csproj file
        CSPROJ_PATH="DrMaxMuscle/DrMaxMuscle.csproj"
        if [ -f "$CSPROJ_PATH" ]; then
          echo "Updating version in $CSPROJ_PATH"

          # Create a backup
          cp "$CSPROJ_PATH" "${CSPROJ_PATH}.bak"

          # Update ApplicationDisplayVersion and ApplicationVersion
          sed -i "s/<ApplicationDisplayVersion>[^<]*<\/ApplicationDisplayVersion>/<ApplicationDisplayVersion>$VERSION_NAME<\/ApplicationDisplayVersion>/g" "$CSPROJ_PATH"
          sed -i "s/<ApplicationVersion>[^<]*<\/ApplicationVersion>/<ApplicationVersion>$VERSION_CODE<\/ApplicationVersion>/g" "$CSPROJ_PATH"

          echo "✅ Successfully updated $CSPROJ_PATH"
        fi

        echo "✅ All manifest files updated with version code $VERSION_CODE and version name $VERSION_NAME"

    - name: 🔐 Decode and save service-account.json
      if: steps.check_credentials.outputs.build_allowed == 'true'
      run: |
        echo "${{ secrets.GOOGLE_SERVICE_JSON }}" | base64 -d > service-account.json

    - name: Build Android App
      if: steps.check_credentials.outputs.build_allowed == 'true'
      id: build_android
      run: |
        # Record build start time
        BUILD_START_TIME=$(date +%s)
        echo "build-start-time=$BUILD_START_TIME" >> $GITHUB_OUTPUT
        echo "Android build compilation started at: $(date -d @$BUILD_START_TIME)"

        # Get absolute path to keystore
        ABSOLUTE_PATH=$(pwd)/keystoreFile.keystore
        echo "Using absolute path to keystore: $ABSOLUTE_PATH"

        # Use the password that was validated in the setup_keystore step
        if [ "${{ steps.setup_keystore.outputs.keystore_valid }}" = "true" ]; then
          KEYSTORE_PASSWORD="${{ steps.setup_keystore.outputs.keystore_password }}"
          KEY_PASSWORD="${{ steps.setup_keystore.outputs.key_password }}"
          echo "Using validated keystore password from previous step"
        else
          # Use default password if validation failed but we're proceeding anyway
          KEYSTORE_PASSWORD="${{ secrets.KEYSTORE_PASSWORD }}"
          KEY_PASSWORD="${{ secrets.KEYSTORE_PASSWORD }}"
          echo "Using keystore password from secrets"
        fi

        # Create a temporary file to capture build output
        BUILD_LOG_FILE="build_output.log"

        # Publish with explicit parameters and capture output
        dotnet publish DrMaxMuscle/DrMaxMuscle.csproj -c Release -f net8.0-android \
          /p:AndroidPackageFormat=aab \
          /p:AndroidKeyStore=True \
          /p:AndroidSigningKeyStore="$ABSOLUTE_PATH" \
          /p:AndroidSigningStorePass="$KEYSTORE_PASSWORD" \
          /p:AndroidSigningKeyAlias="publishingdoc" \
          /p:AndroidSigningKeyPass="$KEY_PASSWORD" \
          /p:KeyPass="$KEY_PASSWORD" \
          /p:AndroidSigningEnabled=True \
          /p:AndroidBuildApplicationPackage=True \
          /p:AndroidUseApkSigner=True \
          /p:SkipInvalidConfigurations=true \
          /p:TargetFrameworks=net8.0-android \
          /p:AndroidVersionCode="${{ needs.workflow-start.outputs.android_version_code }}" \
          /p:AndroidVersionName="${{ steps.version.outputs.version_name }}" \
          /p:AndroidTargetSdkVersion=34 2>&1 | tee "$BUILD_LOG_FILE"

        # Record build end time and calculate duration
        BUILD_END_TIME=$(date +%s)
        echo "build-end-time=$BUILD_END_TIME" >> $GITHUB_OUTPUT
        BUILD_DURATION=$((BUILD_END_TIME - BUILD_START_TIME))
        echo "build-duration=$BUILD_DURATION" >> $GITHUB_OUTPUT
        echo "Android build compilation completed at: $(date -d @$BUILD_END_TIME)"
        echo "Build duration: $(($BUILD_DURATION / 60))m $(($BUILD_DURATION % 60))s"

        # Check if build was successful
        BUILD_RESULT=$?
        if [ $BUILD_RESULT -eq 0 ]; then
          echo "build_success=true" >> $GITHUB_OUTPUT
          echo "✅ Build completed successfully"
        else
          echo "build_success=false" >> $GITHUB_OUTPUT
          echo "❌ Build failed with exit code $BUILD_RESULT"

          # Extract error messages from the log
          echo "Extracting error messages from build log..."
          ERROR_MESSAGES=$(grep -A 3 -B 1 "error [A-Z0-9]*:" "$BUILD_LOG_FILE" | head -n 20)

          if [ -n "$ERROR_MESSAGES" ]; then
            # Escape newlines and quotes for GitHub output
            ERROR_MESSAGES_ESCAPED=$(echo "$ERROR_MESSAGES" | sed ':a;N;$!ba;s/\n/\\n/g' | sed 's/"/\\"/g')
            echo "build_errors<<EOF" >> $GITHUB_OUTPUT
            echo "$ERROR_MESSAGES_ESCAPED" >> $GITHUB_OUTPUT
            echo "EOF" >> $GITHUB_OUTPUT

            echo "Found the following errors:"
            echo "$ERROR_MESSAGES"
          else
            echo "No specific error messages found in the build log."
            echo "build_errors=No specific error messages found. Check the full log for details." >> $GITHUB_OUTPUT
          fi

          # Also check for warnings about API level mismatch
          API_WARNINGS=$(grep -A 1 -B 1 "warning XA1006:" "$BUILD_LOG_FILE" | head -n 10)
          if [ -n "$API_WARNINGS" ]; then
            echo "Found API level mismatch warnings:"
            echo "$API_WARNINGS"

            API_WARNINGS_ESCAPED=$(echo "$API_WARNINGS" | sed ':a;N;$!ba;s/\n/\\n/g' | sed 's/"/\\"/g')
            echo "api_warnings<<EOF" >> $GITHUB_OUTPUT
            echo "$API_WARNINGS_ESCAPED" >> $GITHUB_OUTPUT
            echo "EOF" >> $GITHUB_OUTPUT
          fi
        fi

        # List output directories to see what files were generated
        echo "Listing output directories:"
        echo "=== DrMaxMuscle/bin/Release/net8.0-android/ ==="
        find DrMaxMuscle/bin/Release/net8.0-android/ -type f 2>/dev/null || echo "Directory not found"
        echo "=== DrMaxMuscle/bin/Release/net8.0-android/publish/ ==="
        find DrMaxMuscle/bin/Release/net8.0-android/publish/ -type f 2>/dev/null || echo "Directory not found"
        echo "=== Searching for AAB files specifically ==="
        find DrMaxMuscle/bin/Release/ -name "*.aab" -type f 2>/dev/null || echo "No AAB files found"

    - name: Run Basic Automated Tests
      if: steps.check_credentials.outputs.build_allowed == 'true' && steps.build_android.outputs.build_success == 'true'
      id: run_tests
      run: |
        echo "Running basic automated tests to catch obvious issues..."

        # Check for common build issues
        echo "1. Checking for build output existence..."
        if [ -d "DrMaxMuscle/bin/Release/net8.0-android/" ]; then
          echo "✅ Build output directory exists"
        else
          echo "❌ Build output directory not found!"
          echo "tests_passed=false" >> $GITHUB_OUTPUT
          exit 1
        fi

        # Validate manifest files
        echo "2. Validating AndroidManifest.xml files..."
        MANIFEST_FILES=$(find . -name "AndroidManifest.xml" -type f)
        for MANIFEST in $MANIFEST_FILES; do
          echo "Checking $MANIFEST"
          # With auto-generated date-based version codes, we no longer need to check for minimum version
          echo "✅ $MANIFEST has auto-generated version code: $VERSION_CODE"
        done

        # Check package name consistency
        echo "3. Checking package name consistency..."
        if grep -q "com.drmaxmuscle.dr_max_muscle" DrMaxMuscle/DrMaxMuscle.csproj; then
          echo "✅ Package name is consistent in project file"
        else
          echo "⚠️ Package name may not be consistent in project file"
          echo "tests_warning=true" >> $GITHUB_OUTPUT
        fi

        echo "✅ All basic tests completed"
        echo "tests_passed=true" >> $GITHUB_OUTPUT

    - name: Find AAB file
      if: steps.check_credentials.outputs.build_allowed == 'true' && steps.build_android.outputs.build_success == 'true' && steps.run_tests.outputs.tests_passed != 'false'
      id: find_package
      run: |
        # Search for AAB files in output directory
        echo "Searching for AAB files in output directory..."

        # Define possible locations (publish directory is primary for dotnet publish)
        LOCATIONS=(
          "DrMaxMuscle/bin/Release/net8.0-android/publish/"
          "DrMaxMuscle/bin/Release/net8.0-android/"
          "DrMaxMuscle/bin/Release/"
        )

        # Search in each location
        for LOCATION in "${LOCATIONS[@]}"; do
          if [ -d "$LOCATION" ]; then
            AAB_FILES=$(find "$LOCATION" -name "*.aab" -type f)
            if [ -n "$AAB_FILES" ]; then
              # Use the first AAB file found
              AAB_FILE=$(echo "$AAB_FILES" | head -n 1)
              echo "package_path=$AAB_FILE" >> $GITHUB_OUTPUT
              echo "package_found=true" >> $GITHUB_OUTPUT
              echo "✅ AAB file found at: $AAB_FILE"
              break
            fi
          fi
        done

        # If no AAB file was found
        if [ -z "${AAB_FILE}" ]; then
          echo "❌ No AAB file found in any of the expected locations."
          echo "package_found=false" >> $GITHUB_OUTPUT
        fi

    - name: APK/AAB Analysis
      if: steps.check_credentials.outputs.build_allowed == 'true' && steps.find_package.outputs.package_found == 'true'
      id: aab_analysis
      run: |
        AAB_PATH="${{ steps.find_package.outputs.package_path }}"
        echo "🔍 Analyzing AAB file: $AAB_PATH"

        # Record analysis start time
        ANALYSIS_START_TIME=$(date +%s)
        echo "analysis-start-time=$ANALYSIS_START_TIME" >> $GITHUB_OUTPUT

        # 1. Size tracking
        AAB_SIZE=$(stat -c%s "$AAB_PATH")
        AAB_SIZE_MB=$(echo "scale=2; $AAB_SIZE / 1024 / 1024" | bc -l)
        echo "aab-size-bytes=$AAB_SIZE" >> $GITHUB_OUTPUT
        echo "aab-size-mb=$AAB_SIZE_MB" >> $GITHUB_OUTPUT
        echo "📦 AAB Size: ${AAB_SIZE_MB} MB (${AAB_SIZE} bytes)"

        # 2. Install bundletool for analysis
        echo "📥 Installing bundletool..."
        wget -q https://github.com/google/bundletool/releases/download/1.15.6/bundletool-all-1.15.6.jar -O bundletool.jar

        # 3. Security scanning with bundletool
        echo "🔒 Running security analysis..."
        java -jar bundletool.jar validate --bundle="$AAB_PATH" > security_report.txt 2>&1 || true
        if [ -s security_report.txt ]; then
          echo "security-issues-found=true" >> $GITHUB_OUTPUT
          echo "⚠️ Security analysis found potential issues:"
          cat security_report.txt
        else
          echo "security-issues-found=false" >> $GITHUB_OUTPUT
          echo "✅ No security issues detected"
        fi

        # 4. Extract APK for further analysis
        echo "📱 Extracting APK from AAB for analysis..."
        java -jar bundletool.jar build-apks --bundle="$AAB_PATH" --output=app.apks --mode=universal
        unzip -q app.apks universal.apk

        # 5. Dependency analysis
        echo "📋 Analyzing dependencies..."
        unzip -q universal.apk -d apk_contents/

        # Count native libraries
        NATIVE_LIBS=$(find apk_contents/lib/ -name "*.so" 2>/dev/null | wc -l || echo "0")
        echo "native-lib-count=$NATIVE_LIBS" >> $GITHUB_OUTPUT
        echo "🔧 Native libraries: $NATIVE_LIBS"

        # Analyze APK structure
        APK_SIZE=$(stat -c%s "universal.apk")
        APK_SIZE_MB=$(echo "scale=2; $APK_SIZE / 1024 / 1024" | bc -l)
        echo "apk-size-bytes=$APK_SIZE" >> $GITHUB_OUTPUT
        echo "apk-size-mb=$APK_SIZE_MB" >> $GITHUB_OUTPUT
        echo "📱 Universal APK Size: ${APK_SIZE_MB} MB"

        # 6. Performance profiling preparation
        echo "⚡ Preparing performance analysis..."

        # Check for large assets
        LARGE_ASSETS=$(find apk_contents/ -type f -size +1M 2>/dev/null | wc -l || echo "0")
        echo "large-assets-count=$LARGE_ASSETS" >> $GITHUB_OUTPUT
        echo "📁 Large assets (>1MB): $LARGE_ASSETS"

        # Check DEX file count (affects startup time)
        DEX_COUNT=$(find apk_contents/ -name "*.dex" 2>/dev/null | wc -l || echo "0")
        echo "dex-file-count=$DEX_COUNT" >> $GITHUB_OUTPUT
        echo "🔄 DEX files: $DEX_COUNT"

        # Record analysis end time
        ANALYSIS_END_TIME=$(date +%s)
        ANALYSIS_DURATION=$((ANALYSIS_END_TIME - ANALYSIS_START_TIME))
        echo "analysis-duration=$ANALYSIS_DURATION" >> $GITHUB_OUTPUT
        echo "⏱️ Analysis completed in: $(($ANALYSIS_DURATION / 60))m $(($ANALYSIS_DURATION % 60))s"

    - name: Move AAB to app-release
      if: steps.check_credentials.outputs.build_allowed == 'true' && steps.find_package.outputs.package_found == 'true'
      run: |
        # Create app-release directory if it doesn't exist
        mkdir -p app-release

        # Copy the AAB file to app-release directory
        cp "${{ steps.find_package.outputs.package_path }}" app-release/com.drmaxmuscle.dr_max_muscle-Signed.aab
        echo "✅ AAB file copied to app-release directory"

        # Verify the file exists
        if [ -f "app-release/com.drmaxmuscle.dr_max_muscle-Signed.aab" ]; then
          echo "Verified AAB file exists at app-release/com.drmaxmuscle.dr_max_muscle-Signed.aab"
          ls -la app-release/
        else
          echo "❌ Failed to copy AAB file to app-release directory"
          exit 1
        fi

    - name: Sign AAB
      if: steps.check_credentials.outputs.build_allowed == 'true' && steps.find_package.outputs.package_found == 'true'
      run: |
        jarsigner -verbose -sigalg SHA256withRSA -digestalg SHA-256 \
          -keystore keystoreFile.keystore \
          -storepass "${{ secrets.KEYSTORE_PASSWORD }}" \
          -keypass "${{ secrets.KEYSTORE_PASSWORD }}" \
          app-release/com.drmaxmuscle.dr_max_muscle-Signed.aab \
          publishingdoc

    - name: Decode Google Play JSON Key
      if: steps.check_credentials.outputs.credentials_valid == 'true' && steps.find_package.outputs.package_found == 'true'
      run: echo "${{ secrets.GOOGLE_SERVICE_JSON }}" | base64 --decode > google-play-key.json

    - name: Upload to Play Store Internal Testing
      if: steps.check_credentials.outputs.credentials_valid == 'true' && steps.find_package.outputs.package_found == 'true'
      uses: r0adkll/upload-google-play@v1
      with:
        serviceAccountJson: google-play-key.json
        packageName: com.drmaxmuscle.dr_max_muscle  # Change to your package name
        releaseFiles: app-release/com.drmaxmuscle.dr_max_muscle-Signed.aab
        track: internal
        status: completed  # Change from "draft" to "completed"


    # Output build summary
    - name: Output Build Summary
      if: always()
      run: |
        echo "### 📱 Android Build Summary" >> $GITHUB_STEP_SUMMARY
        echo "- **Selected Platform:** ${{ github.event.inputs.platform }}" >> $GITHUB_STEP_SUMMARY

        # Add performance metrics with clean time format
        if [ -n "${{ steps.build_android.outputs.build-duration }}" ]; then
          BUILD_DURATION="${{ steps.build_android.outputs.build-duration }}"
          echo "- **Build Duration:** $(($BUILD_DURATION / 60))m $(($BUILD_DURATION % 60))s" >> $GITHUB_STEP_SUMMARY
        fi

        # Add cache performance metrics
        echo "- **Cache Performance:**" >> $GITHUB_STEP_SUMMARY
        if [ "${{ steps.cache-nuget.outputs.cache-hit }}" = "true" ]; then
          echo "  - NuGet: ✅ Cache Hit" >> $GITHUB_STEP_SUMMARY
        else
          echo "  - NuGet: ❌ Cache Miss" >> $GITHUB_STEP_SUMMARY
        fi
        if [ "${{ steps.cache-android-sdk.outputs.cache-hit }}" = "true" ]; then
          echo "  - Android SDK: ✅ Cache Hit" >> $GITHUB_STEP_SUMMARY
        else
          echo "  - Android SDK: ❌ Cache Miss" >> $GITHUB_STEP_SUMMARY
        fi

        # Add build step breakdown
        if [ -n "${{ steps.restore_deps.outputs.restore-duration }}" ]; then
          RESTORE_DURATION="${{ steps.restore_deps.outputs.restore-duration }}"
          echo "- **Build Step Breakdown:**" >> $GITHUB_STEP_SUMMARY
          echo "  - Dependency Restore: $(($RESTORE_DURATION / 60))m $(($RESTORE_DURATION % 60))s" >> $GITHUB_STEP_SUMMARY
        fi

        # Add keystore information
        if [ "${{ steps.setup_keystore.outputs.keystore_valid }}" = "true" ]; then
          echo "- **Keystore:** ✅ Repository keystore used" >> $GITHUB_STEP_SUMMARY
          echo "- **Keystore Path:** \`./publishingdoc.keystore\`" >> $GITHUB_STEP_SUMMARY
        else
          echo "- **Keystore:** ❌ Keystore validation failed" >> $GITHUB_STEP_SUMMARY
        fi

        # Add Google Play service account information
        if [ "${{ steps.validate_gplay.outputs.service_account_valid }}" = "true" ]; then
          echo "- **Google Play Service Account:** ✅ Valid" >> $GITHUB_STEP_SUMMARY
        else
          echo "- **Google Play Service Account:** ❌ Invalid" >> $GITHUB_STEP_SUMMARY
        fi

        # Add build status information
        if [ "${{ steps.build_android.outputs.build_success }}" = "true" ]; then
          echo "- **Build:** ✅ Completed successfully" >> $GITHUB_STEP_SUMMARY
        elif [ "${{ steps.build_android.outputs.build_success }}" = "false" ]; then
          echo "- **Build:** ❌ Failed" >> $GITHUB_STEP_SUMMARY

          # Add API warnings if any
          if [ -n "${{ steps.build_android.outputs.api_warnings }}" ]; then
            echo "  - **API Level Warnings:**" >> $GITHUB_STEP_SUMMARY
            echo "    ```" >> $GITHUB_STEP_SUMMARY
            echo "${{ steps.build_android.outputs.api_warnings }}" >> $GITHUB_STEP_SUMMARY
            echo "    ```" >> $GITHUB_STEP_SUMMARY
          fi

          # Add build errors if any
          if [ -n "${{ steps.build_android.outputs.build_errors }}" ]; then
            echo "  - **Build Errors:**" >> $GITHUB_STEP_SUMMARY
            echo "    ```" >> $GITHUB_STEP_SUMMARY
            echo "${{ steps.build_android.outputs.build_errors }}" >> $GITHUB_STEP_SUMMARY
            echo "    ```" >> $GITHUB_STEP_SUMMARY
          fi
        else
          echo "- **Build:** ⚠️ Status unknown" >> $GITHUB_STEP_SUMMARY
        fi

        # Add test results
        if [ "${{ steps.run_tests.outputs.tests_passed }}" = "true" ]; then
          echo "- **Tests:** ✅ All basic tests passed" >> $GITHUB_STEP_SUMMARY
          if [ "${{ steps.run_tests.outputs.tests_warning }}" = "true" ]; then
            echo "  - ⚠️ Some non-critical warnings detected" >> $GITHUB_STEP_SUMMARY
          fi
        elif [ "${{ steps.run_tests.outputs.tests_passed }}" = "false" ]; then
          echo "- **Tests:** ❌ Some tests failed" >> $GITHUB_STEP_SUMMARY
        else
          echo "- **Tests:** ⚠️ Tests were not run" >> $GITHUB_STEP_SUMMARY
        fi

        # Add package information if found
        if [ "${{ steps.find_package.outputs.package_found }}" = "true" ]; then
          echo "- **Package:** ✅ Built successfully" >> $GITHUB_STEP_SUMMARY
          echo "- **Package Path:** \`${{ steps.find_package.outputs.package_path }}\`" >> $GITHUB_STEP_SUMMARY
          echo "- **Android code:** ${{ needs.workflow-start.outputs.android_version_code }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Android Target SDK:** 34" >> $GITHUB_STEP_SUMMARY

          # Add APK/AAB analysis results
          if [ -n "${{ steps.aab_analysis.outputs.aab-size-mb }}" ]; then
            echo "- **Package Analysis:**" >> $GITHUB_STEP_SUMMARY
            echo "  - AAB Size: ${{ steps.aab_analysis.outputs.aab-size-mb }} MB" >> $GITHUB_STEP_SUMMARY
            echo "  - APK Size: ${{ steps.aab_analysis.outputs.apk-size-mb }} MB" >> $GITHUB_STEP_SUMMARY
            echo "  - Native Libraries: ${{ steps.aab_analysis.outputs.native-lib-count }}" >> $GITHUB_STEP_SUMMARY
            echo "  - DEX Files: ${{ steps.aab_analysis.outputs.dex-file-count }}" >> $GITHUB_STEP_SUMMARY
            echo "  - Large Assets (>1MB): ${{ steps.aab_analysis.outputs.large-assets-count }}" >> $GITHUB_STEP_SUMMARY

            if [ "${{ steps.aab_analysis.outputs.security-issues-found }}" = "true" ]; then
              echo "  - Security: ⚠️ Issues detected" >> $GITHUB_STEP_SUMMARY
            else
              echo "  - Security: ✅ No issues detected" >> $GITHUB_STEP_SUMMARY
            fi

            if [ -n "${{ steps.aab_analysis.outputs.analysis-duration }}" ]; then
              ANALYSIS_DURATION="${{ steps.aab_analysis.outputs.analysis-duration }}"
              echo "  - Analysis Time: $(($ANALYSIS_DURATION / 60))m $(($ANALYSIS_DURATION % 60))s" >> $GITHUB_STEP_SUMMARY
            fi
          fi

          # Add deployment information
          if [ "${{ steps.check_credentials.outputs.credentials_valid }}" = "true" ]; then
            echo "- **Deployment:** ✅ Uploaded to Google Play internal testing track" >> $GITHUB_STEP_SUMMARY
            echo "- **Track:** internal" >> $GITHUB_STEP_SUMMARY
            echo "- **Package Name:** com.drmaxmuscle.dr_max_muscle" >> $GITHUB_STEP_SUMMARY
            VERSION_CODE="${{ needs.workflow-start.outputs.android_version_code }}"
            ANDROID_DOWNLOAD_URL="https://play.google.com/apps/test/com.drmaxmuscle.dr_max_muscle/${VERSION_CODE}"
            echo "- **Download:** <a href=\"${ANDROID_DOWNLOAD_URL}\" target=\"_blank\">Direct Download Link</a>" >> $GITHUB_STEP_SUMMARY
          else
            echo "- **Deployment:** ⚠️ Not deployed to Google Play" >> $GITHUB_STEP_SUMMARY
            echo "  - ℹ️ Reason: Credentials validation failed" >> $GITHUB_STEP_SUMMARY
          fi
        elif [ "${{ steps.build_android.outputs.build_success }}" = "false" ]; then
          echo "- **Package:** ❌ Build failed, no package was generated" >> $GITHUB_STEP_SUMMARY
        elif [ "${{ steps.check_credentials.outputs.build_allowed }}" = "true" ]; then
          echo "- **Package:** ❌ Build process completed but no package was found" >> $GITHUB_STEP_SUMMARY
        else
          echo "- **Package:** ❌ Build skipped due to missing credentials" >> $GITHUB_STEP_SUMMARY
        fi

  # Performance metrics job to calculate total workflow runtime
  performance-metrics:
    name: Performance Metrics
    runs-on: ubicloud-standard-2
    if: always()
    needs: [workflow-start, build-ios, build-android]
    steps:
    - name: Calculate and output performance metrics
      run: |
        # Calculate total workflow runtime
        WORKFLOW_END_TIME=$(date +%s)
        WORKFLOW_START_TIME="${{ needs.workflow-start.outputs.start-time }}"
        TOTAL_WORKFLOW_DURATION=$((WORKFLOW_END_TIME - WORKFLOW_START_TIME))

        echo "### ⏱️ Performance Metrics" >> $GITHUB_STEP_SUMMARY
        echo "- **Workflow Started:** $(date -d @$WORKFLOW_START_TIME)" >> $GITHUB_STEP_SUMMARY
        echo "- **Workflow Completed:** $(date -d @$WORKFLOW_END_TIME)" >> $GITHUB_STEP_SUMMARY

        # Add Android build metrics if Android was built
        if [ "${{ github.event.inputs.platform }}" = "iOS & Android" ] || [ "${{ github.event.inputs.platform }}" = "Android" ]; then
          if [ "${{ needs.build-android.result }}" = "success" ]; then
            if [ -n "${{ needs.build-android.outputs.build-duration }}" ]; then
              ANDROID_BUILD_DURATION="${{ needs.build-android.outputs.build-duration }}"
              echo "- **Android Build Duration:** $(($ANDROID_BUILD_DURATION / 60))m $(($ANDROID_BUILD_DURATION % 60))s" >> $GITHUB_STEP_SUMMARY
            fi

            # Calculate Android job total time vs build time
            ANDROID_JOB_START="${{ needs.build-android.outputs.android-start-time }}"
            ANDROID_BUILD_START="${{ needs.build-android.outputs.build-start-time }}"
            ANDROID_BUILD_END="${{ needs.build-android.outputs.build-end-time }}"

            if [ -n "$ANDROID_JOB_START" ] && [ -n "$ANDROID_BUILD_START" ] && [ -n "$ANDROID_BUILD_END" ]; then
              ANDROID_PREP_TIME=$((ANDROID_BUILD_START - ANDROID_JOB_START))
              ANDROID_BUILD_TIME="${{ needs.build-android.outputs.build-duration }}"
              ANDROID_POST_TIME=$((WORKFLOW_END_TIME - ANDROID_BUILD_END))

              echo "- **Android Job Breakdown:**" >> $GITHUB_STEP_SUMMARY
              echo "  - Setup & Preparation: $(($ANDROID_PREP_TIME / 60))m $(($ANDROID_PREP_TIME % 60))s" >> $GITHUB_STEP_SUMMARY
              echo "  - Actual Build: $(($ANDROID_BUILD_TIME / 60))m $(($ANDROID_BUILD_TIME % 60))s" >> $GITHUB_STEP_SUMMARY
              echo "  - Post-build Processing: $(($ANDROID_POST_TIME / 60))m $(($ANDROID_POST_TIME % 60))s" >> $GITHUB_STEP_SUMMARY
            fi
          else
            echo "- **Android Build:** ❌ Failed or skipped" >> $GITHUB_STEP_SUMMARY
          fi
        fi

        # Add iOS build metrics if iOS was built
        if [ "${{ github.event.inputs.platform }}" = "iOS & Android" ] || [ "${{ github.event.inputs.platform }}" = "iOS" ]; then
          if [ "${{ needs.build-ios.result }}" = "success" ]; then
            if [ -n "${{ needs.build-ios.outputs.build-duration }}" ]; then
              IOS_BUILD_DURATION="${{ needs.build-ios.outputs.build-duration }}"
              echo "- **iOS Build Duration:** $(($IOS_BUILD_DURATION / 60))m $(($IOS_BUILD_DURATION % 60))s" >> $GITHUB_STEP_SUMMARY
            fi

            # Calculate iOS job total time vs build time
            IOS_JOB_START="${{ needs.build-ios.outputs.ios-start-time }}"
            IOS_BUILD_START="${{ needs.build-ios.outputs.build-start-time }}"
            IOS_BUILD_END="${{ needs.build-ios.outputs.build-end-time }}"

            if [ -n "$IOS_JOB_START" ] && [ -n "$IOS_BUILD_START" ] && [ -n "$IOS_BUILD_END" ]; then
              IOS_PREP_TIME=$((IOS_BUILD_START - IOS_JOB_START))
              IOS_BUILD_TIME="${{ needs.build-ios.outputs.build-duration }}"
              IOS_POST_TIME=$((WORKFLOW_END_TIME - IOS_BUILD_END))

              echo "- **iOS Job Breakdown:**" >> $GITHUB_STEP_SUMMARY
              echo "  - Setup & Preparation: $(($IOS_PREP_TIME / 60))m $(($IOS_PREP_TIME % 60))s" >> $GITHUB_STEP_SUMMARY
              echo "  - Actual Build: $(($IOS_BUILD_TIME / 60))m $(($IOS_BUILD_TIME % 60))s" >> $GITHUB_STEP_SUMMARY
              echo "  - Post-build Processing: $(($IOS_POST_TIME / 60))m $(($IOS_POST_TIME % 60))s" >> $GITHUB_STEP_SUMMARY
            fi
          else
            echo "- **iOS Build:** ❌ Failed or skipped" >> $GITHUB_STEP_SUMMARY
          fi
        fi

        # Add total workflow runtime at the bottom
        echo "- **Total Workflow Runtime:** $(($TOTAL_WORKFLOW_DURATION / 60))m $(($TOTAL_WORKFLOW_DURATION % 60))s" >> $GITHUB_STEP_SUMMARY

    - name: Store Historical Performance Metrics
      if: always()
      uses: actions/github-script@v7
      with:
        script: |
          // Calculate total workflow duration
          const workflowStartTime = parseInt('${{ needs.workflow-start.outputs.start-time }}');
          const workflowEndTime = Math.floor(Date.now() / 1000);
          const totalWorkflowDuration = workflowEndTime - workflowStartTime;

          // Collect all metrics
          const metrics = {
            timestamp: new Date().toISOString(),
            runNumber: context.runNumber,
            runId: context.runId,
            platform: '${{ github.event.inputs.platform }}',
            branch: context.ref.replace('refs/heads/', ''),
            workflow: {
              totalDuration: totalWorkflowDuration,
              startTime: workflowStartTime,
              endTime: workflowEndTime,
              status: '${{ job.status }}'
            },
            android: {
              buildDuration: '${{ needs.build-android.outputs.build-duration }}' ?
                parseInt('${{ needs.build-android.outputs.build-duration }}') : null,
              restoreDuration: '${{ needs.build-android.outputs.restore-duration }}' ?
                parseInt('${{ needs.build-android.outputs.restore-duration }}') : null,
              buildSuccess: '${{ needs.build-android.result }}' === 'success',
              cacheHits: {
                nuget: '${{ needs.build-android.outputs.nuget-cache-hit }}' === 'true',
                androidSdk: '${{ needs.build-android.outputs.android-sdk-cache-hit }}' === 'true'
              }
            },
            ios: {
              buildDuration: '${{ needs.build-ios.outputs.build-duration }}' ?
                parseInt('${{ needs.build-ios.outputs.build-duration }}') : null,
              restoreDuration: '${{ needs.build-ios.outputs.restore-duration }}' ?
                parseInt('${{ needs.build-ios.outputs.restore-duration }}') : null,
              buildSuccess: '${{ needs.build-ios.result }}' === 'success',
              cacheHits: {
                dotnet: '${{ needs.build-ios.outputs.dotnet-cache-hit }}' === 'true',
                nuget: '${{ needs.build-ios.outputs.nuget-cache-hit }}' === 'true'
              },
              testflightUpload: '${{ needs.build-ios.outputs.testflight-upload-success }}' === 'true'
            },
            package: {
              aabSizeMB: '${{ needs.build-android.outputs.aab-size-mb }}' ?
                parseFloat('${{ needs.build-android.outputs.aab-size-mb }}') : null,
              apkSizeMB: '${{ needs.build-android.outputs.apk-size-mb }}' ?
                parseFloat('${{ needs.build-android.outputs.apk-size-mb }}') : null,
              ipaSizeMB: '${{ needs.build-ios.outputs.ipa-size-mb }}' ?
                parseFloat('${{ needs.build-ios.outputs.ipa-size-mb }}') : null,
              nativeLibCount: '${{ needs.build-android.outputs.native-lib-count }}' ?
                parseInt('${{ needs.build-android.outputs.native-lib-count }}') : null,
              dexFileCount: '${{ needs.build-android.outputs.dex-file-count }}' ?
                parseInt('${{ needs.build-android.outputs.dex-file-count }}') : null,
              largeAssetsCount: '${{ needs.build-android.outputs.large-assets-count }}' ?
                parseInt('${{ needs.build-android.outputs.large-assets-count }}') : null,
              securityIssues: '${{ needs.build-android.outputs.security-issues-found }}' === 'true'
            }
          };

          // Find or create performance tracking issue
          const issues = await github.rest.issues.listForRepo({
            owner: context.repo.owner,
            repo: context.repo.repo,
            labels: ['performance-tracking', 'ci-cd'],
            state: 'open'
          });

          let issue = issues.data[0];
          if (!issue) {
            const { data: newIssue } = await github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: '📊 CI/CD Performance Metrics Tracking',
              body: `# 📊 CI/CD Performance Metrics Tracking

          This issue automatically tracks build performance metrics over time with trend analysis and alerts.

          ## 📈 Performance Dashboard
          *Dashboard will be updated automatically with each build*

          ## 🎯 Key Performance Indicators
          - **Build Efficiency Score**: Composite score based on duration, cache hits, and success rate
          - **Trend Analysis**: 7-day and 30-day moving averages
          - **Performance Alerts**: Automatic detection of regressions and improvements

          ## 📋 Metrics Collected
          - Build duration and breakdown with trend analysis
          - Package sizes (AAB/APK) with growth tracking
          - Cache hit rates (NuGet, Android SDK) with effectiveness scoring
          - Security scan results and issue tracking
          - Native library and DEX file counts with optimization suggestions

          ## 🚨 Alert Thresholds
          - **Build Duration**: >20% increase triggers regression alert
          - **Package Size**: >10% increase triggers size alert
          - **Cache Miss Streak**: 3+ consecutive misses triggers cache alert

          ---
          *Metrics and trends are automatically posted as comments below*`,
              labels: ['performance-tracking', 'ci-cd', 'automation']
            });
            issue = newIssue;
          }

          // Fetch historical data from existing comments for trend analysis
          const comments = await github.rest.issues.listComments({
            owner: context.repo.owner,
            repo: context.repo.repo,
            issue_number: issue.number,
            per_page: 50
          });

          // Parse historical metrics from comments
          const historicalMetrics = [];
          for (const comment of comments.data) {
            try {
              const jsonMatch = comment.body.match(/```json\n([\s\S]*?)\n```/);
              if (jsonMatch) {
                const data = JSON.parse(jsonMatch[1]);
                if (data.timestamp && data.android) {
                  historicalMetrics.push(data);
                }
              }
            } catch (e) {
              // Skip invalid JSON
            }
          }

          // Sort by timestamp (newest first)
          historicalMetrics.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

          // Calculate trends and analytics
          const analytics = {
            trends: {},
            alerts: [],
            kpis: {}
          };

          if (historicalMetrics.length > 0) {
            // Build duration trend (last 7 builds)
            const recentBuilds = historicalMetrics.slice(0, 7).filter(m => m.android.buildDuration);
            if (recentBuilds.length > 1) {
              const avgDuration = recentBuilds.reduce((sum, m) => sum + m.android.buildDuration, 0) / recentBuilds.length;
              const currentDuration = metrics.android.buildDuration || 0;
              const trendPercent = ((currentDuration - avgDuration) / avgDuration * 100).toFixed(1);

              analytics.trends.buildDuration = {
                current: currentDuration,
                average: Math.round(avgDuration),
                trendPercent: parseFloat(trendPercent),
                direction: trendPercent > 0 ? '📈' : '📉'
              };

              // Performance regression alert
              if (Math.abs(trendPercent) > 20) {
                analytics.alerts.push({
                  type: trendPercent > 0 ? 'regression' : 'improvement',
                  metric: 'Build Duration',
                  change: `${trendPercent}%`,
                  icon: trendPercent > 0 ? '🚨' : '🎉'
                });
              }
            }

            // Package size trend
            const recentPackages = historicalMetrics.slice(0, 7).filter(m => m.package.aabSizeMB);
            if (recentPackages.length > 1 && metrics.package.aabSizeMB) {
              const avgSize = recentPackages.reduce((sum, m) => sum + m.package.aabSizeMB, 0) / recentPackages.length;
              const sizeChange = ((metrics.package.aabSizeMB - avgSize) / avgSize * 100).toFixed(1);

              analytics.trends.packageSize = {
                current: metrics.package.aabSizeMB,
                average: avgSize.toFixed(2),
                trendPercent: parseFloat(sizeChange),
                direction: sizeChange > 0 ? '📈' : '📉'
              };

              // Package size alert
              if (Math.abs(sizeChange) > 10) {
                analytics.alerts.push({
                  type: sizeChange > 0 ? 'size-increase' : 'size-decrease',
                  metric: 'Package Size',
                  change: `${sizeChange}%`,
                  icon: sizeChange > 0 ? '⚠️' : '✅'
                });
              }
            }

            // Cache effectiveness analysis
            const recentCacheData = historicalMetrics.slice(0, 10);
            const nugetHitRate = recentCacheData.filter(m => m.android.cacheHits.nuget).length / recentCacheData.length * 100;
            const sdkHitRate = recentCacheData.filter(m => m.android.cacheHits.androidSdk).length / recentCacheData.length * 100;

            analytics.trends.cacheEffectiveness = {
              nugetHitRate: nugetHitRate.toFixed(1),
              sdkHitRate: sdkHitRate.toFixed(1),
              overall: ((nugetHitRate + sdkHitRate) / 2).toFixed(1)
            };

            // Cache miss streak detection
            let consecutiveMisses = 0;
            for (const build of recentCacheData) {
              if (!build.android.cacheHits.nuget || !build.android.cacheHits.androidSdk) {
                consecutiveMisses++;
              } else {
                break;
              }
            }

            if (consecutiveMisses >= 3) {
              analytics.alerts.push({
                type: 'cache-miss-streak',
                metric: 'Cache Performance',
                change: `${consecutiveMisses} consecutive misses`,
                icon: '🐌'
              });
            }

            // Calculate Build Efficiency Score (0-100)
            let efficiencyScore = 100;

            // Deduct points for slow builds (baseline: 10 minutes)
            if (metrics.android.buildDuration > 600) {
              efficiencyScore -= Math.min(30, (metrics.android.buildDuration - 600) / 60 * 2);
            }

            // Deduct points for cache misses
            if (!metrics.android.cacheHits.nuget) efficiencyScore -= 15;
            if (!metrics.android.cacheHits.androidSdk) efficiencyScore -= 15;

            // Deduct points for build failure
            if (!metrics.android.buildSuccess) efficiencyScore -= 40;

            // Deduct points for security issues
            if (metrics.package.securityIssues) efficiencyScore -= 10;

            analytics.kpis.efficiencyScore = Math.max(0, Math.round(efficiencyScore));
          }

          // Create formatted comment with enhanced metrics and analytics
          const buildStatus = metrics.android.buildSuccess ? '✅' : '❌';
          const cacheStatus = metrics.android.cacheHits.nuget && metrics.android.cacheHits.androidSdk ? '🚀' : '🐌';

          // Generate efficiency score badge
          let efficiencyBadge = '🔴';
          if (analytics.kpis.efficiencyScore >= 80) efficiencyBadge = '🟢';
          else if (analytics.kpis.efficiencyScore >= 60) efficiencyBadge = '🟡';
          else if (analytics.kpis.efficiencyScore >= 40) efficiencyBadge = '🟠';

          // Generate alerts section
          let alertsSection = '';
          if (analytics.alerts.length > 0) {
            alertsSection = '\n### 🚨 Performance Alerts\n';
            for (const alert of analytics.alerts) {
              alertsSection += `- ${alert.icon} **${alert.metric}**: ${alert.change} ${alert.type === 'improvement' ? '(Improvement!)' : '(Attention needed)'}\n`;
            }
          }

          // Generate trends section
          let trendsSection = '';
          if (Object.keys(analytics.trends).length > 0) {
            trendsSection = '\n### 📈 Trend Analysis (Last 7 Builds)\n';

            if (analytics.trends.buildDuration) {
              const trend = analytics.trends.buildDuration;
              trendsSection += `- **Build Duration**: ${Math.floor(trend.current / 60)}m ${trend.current % 60}s ${trend.direction} (${trend.trendPercent > 0 ? '+' : ''}${trend.trendPercent}% vs avg ${Math.floor(trend.average / 60)}m ${trend.average % 60}s)\n`;
            }

            if (analytics.trends.packageSize) {
              const trend = analytics.trends.packageSize;
              trendsSection += `- **Package Size**: ${trend.current} MB ${trend.direction} (${trend.trendPercent > 0 ? '+' : ''}${trend.trendPercent}% vs avg ${trend.average} MB)\n`;
            }

            if (analytics.trends.cacheEffectiveness) {
              const cache = analytics.trends.cacheEffectiveness;
              trendsSection += `- **Cache Effectiveness**: ${cache.overall}% overall (NuGet: ${cache.nugetHitRate}%, SDK: ${cache.sdkHitRate}%)\n`;
            }
          }

          // Generate simple ASCII chart for build duration trend
          let chartSection = '';
          if (analytics.trends.buildDuration && historicalMetrics.length >= 5) {
            chartSection = '\n### 📊 Build Duration Trend (Last 10 Builds)\n```\n';
            const recentDurations = historicalMetrics.slice(0, 10).reverse().map(m => m.android.buildDuration || 0);
            const maxDuration = Math.max(...recentDurations);
            const minDuration = Math.min(...recentDurations.filter(d => d > 0));

            for (let i = 0; i < recentDurations.length; i++) {
              const duration = recentDurations[i];
              if (duration > 0) {
                const barLength = Math.round((duration - minDuration) / (maxDuration - minDuration) * 20) + 1;
                const bar = '█'.repeat(barLength);
                const minutes = Math.floor(duration / 60);
                chartSection += `Build ${i + 1}: ${bar} ${minutes}m\n`;
              }
            }
            chartSection += '```\n';
          }

          const comment = `## ${buildStatus} Build #${metrics.runNumber} - ${metrics.platform}

          **📅 Timestamp:** ${metrics.timestamp}
          **🌿 Branch:** \`${metrics.branch}\`
          **⏱️ Total Workflow Duration:** ${Math.floor(metrics.workflow.totalDuration / 60)}m ${metrics.workflow.totalDuration % 60}s
          ${analytics.kpis.efficiencyScore !== undefined ? `**${efficiencyBadge} Build Efficiency Score:** ${analytics.kpis.efficiencyScore}/100` : ''}

          ${alertsSection}

          ### 🏗️ Build Performance
          - **Build Duration:** ${metrics.android.buildDuration ? Math.floor(metrics.android.buildDuration / 60) + 'm ' + (metrics.android.buildDuration % 60) + 's' : 'N/A'}
          - **Restore Duration:** ${metrics.android.restoreDuration ? Math.floor(metrics.android.restoreDuration / 60) + 'm ' + (metrics.android.restoreDuration % 60) + 's' : 'N/A'}
          - **Build Status:** ${metrics.android.buildSuccess ? '✅ Success' : '❌ Failed'}

          ### ${cacheStatus} Cache Performance
          - **NuGet Cache:** ${metrics.android.cacheHits.nuget ? '✅ Hit' : '❌ Miss'}
          - **Android SDK Cache:** ${metrics.android.cacheHits.androidSdk ? '✅ Hit' : '❌ Miss'}

          ### 📦 Package Analysis
          ${metrics.package.aabSizeMB ? `- **AAB Size:** ${metrics.package.aabSizeMB} MB` : '- **AAB Size:** N/A'}
          ${metrics.package.apkSizeMB ? `- **APK Size:** ${metrics.package.apkSizeMB} MB` : '- **APK Size:** N/A'}
          ${metrics.package.nativeLibCount !== null ? `- **Native Libraries:** ${metrics.package.nativeLibCount}` : '- **Native Libraries:** N/A'}
          ${metrics.package.dexFileCount !== null ? `- **DEX Files:** ${metrics.package.dexFileCount}` : '- **DEX Files:** N/A'}
          ${metrics.package.largeAssetsCount !== null ? `- **Large Assets:** ${metrics.package.largeAssetsCount}` : '- **Large Assets:** N/A'}
          - **Security Issues:** ${metrics.package.securityIssues ? '⚠️ Found' : '✅ None'}

          ${trendsSection}

          ${chartSection}

          <details>
          <summary>📊 Raw Metrics Data</summary>

          \`\`\`json
          ${JSON.stringify(metrics, null, 2)}
          \`\`\`
          </details>

          ---
          *[View Workflow Run](https://github.com/${context.repo.owner}/${context.repo.repo}/actions/runs/${context.runId})*`;

          // Add comment to issue
          await github.rest.issues.createComment({
            owner: context.repo.owner,
            repo: context.repo.repo,
            issue_number: issue.number,
            body: comment
          });

          // Update issue description with latest dashboard if we have enough data
          if (historicalMetrics.length >= 3) {
            const recentBuilds = historicalMetrics.slice(0, 10);
            const successRate = (recentBuilds.filter(m => m.android.buildSuccess).length / recentBuilds.length * 100).toFixed(1);
            const avgBuildTime = recentBuilds.reduce((sum, m) => sum + (m.android.buildDuration || 0), 0) / recentBuilds.length;
            const avgPackageSize = recentBuilds.filter(m => m.package.aabSizeMB).reduce((sum, m) => sum + m.package.aabSizeMB, 0) / recentBuilds.filter(m => m.package.aabSizeMB).length;

            // Build dashboard sections
            const successBuilds = recentBuilds.filter(m => m.android.buildSuccess).length;
            const totalBuilds = recentBuilds.length;

            // Build quick stats chart
            const quickStatsChart = recentBuilds.slice(0, 5).map((m, i) => {
              const duration = m.android.buildDuration || 0;
              const minutes = Math.floor(duration / 60);
              const bar = '█'.repeat(Math.max(1, Math.round(minutes / 2)));
              return 'Build ' + (totalBuilds - i) + ': ' + bar + ' ' + minutes + 'm';
            }).join('\\n');

            // Build alerts section
            let alertsSection = '### ✅ No Active Performance Alerts';
            if (analytics.alerts.length > 0) {
              alertsSection = '### 🚨 Active Alerts\\n' +
                analytics.alerts.map(alert => '- ' + alert.icon + ' **' + alert.metric + '**: ' + alert.change).join('\\n');
            }

            // Build optimization tips
            let optimizationTips = '';
            if (avgBuildTime > 900) {
              optimizationTips += '- ⚠️ **Build time is high** - Consider optimizing dependencies or build steps\\n';
            }
            if (analytics.trends.cacheEffectiveness && parseFloat(analytics.trends.cacheEffectiveness.overall) < 70) {
              optimizationTips += '- ⚠️ **Cache effectiveness is low** - Review cache configuration\\n';
            }
            if (avgPackageSize > 50) {
              optimizationTips += '- ⚠️ **Package size is large** - Consider code splitting or asset optimization\\n';
            }
            if (analytics.trends.cacheEffectiveness && parseFloat(analytics.trends.cacheEffectiveness.overall) >= 90) {
              optimizationTips += '- ✅ **Excellent cache performance** - Keep up the good work!\\n';
            }
            if (avgBuildTime < 600) {
              optimizationTips += '- ✅ **Fast build times** - Great optimization!\\n';
            }

            const dashboardBody = '# 📊 CI/CD Performance Metrics Tracking\n\n' +
              'This issue automatically tracks build performance metrics over time with trend analysis and alerts.\n\n' +
              '## 📈 Performance Dashboard\n' +
              '*Last updated: ' + new Date().toISOString().split('T')[0] + '*\n\n' +
              '### 🎯 Current Performance Summary (Last 10 Builds)\n' +
              '- **Success Rate**: ' + successRate + '% (' + successBuilds + '/' + totalBuilds + ' builds)\n' +
              '- **Average Build Time**: ' + Math.floor(avgBuildTime / 60) + 'm ' + Math.round(avgBuildTime % 60) + 's\n' +
              '- **Average Package Size**: ' + (avgPackageSize ? avgPackageSize.toFixed(2) + ' MB' : 'N/A') + '\n' +
              '- **Latest Efficiency Score**: ' + (analytics.kpis.efficiencyScore !== undefined ? analytics.kpis.efficiencyScore + '/100' : 'N/A') + ' ' + efficiencyBadge + '\n\n' +
              '### 📊 Quick Stats\n' +
              '```\n' +
              'Recent Build Times (minutes):\n' +
              quickStatsChart + '\n' +
              '```\n\n' +
              alertsSection + '\n\n' +
              '## 🎯 Key Performance Indicators\n' +
              '- **Build Efficiency Score**: Composite score based on duration, cache hits, and success rate\n' +
              '- **Trend Analysis**: 7-day and 30-day moving averages\n' +
              '- **Performance Alerts**: Automatic detection of regressions and improvements\n\n' +
              '## 📋 Metrics Collected\n' +
              '- Build duration and breakdown with trend analysis\n' +
              '- Package sizes (AAB/APK) with growth tracking\n' +
              '- Cache hit rates (NuGet, Android SDK) with effectiveness scoring\n' +
              '- Security scan results and issue tracking\n' +
              '- Native library and DEX file counts with optimization suggestions\n\n' +
              '## 🚨 Alert Thresholds\n' +
              '- **Build Duration**: >20% increase triggers regression alert\n' +
              '- **Package Size**: >10% increase triggers size alert\n' +
              '- **Cache Miss Streak**: 3+ consecutive misses triggers cache alert\n\n' +
              '## 📈 Performance Optimization Tips\n' +
              optimizationTips + '\n' +
              '---\n' +
              '*Metrics and trends are automatically posted as comments below*';

            await github.rest.issues.update({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: issue.number,
              body: dashboardBody
            });
          }

  # Download Links Summary - 4th section at the bottom
  download-links:
    name: Download Links
    runs-on: ubicloud-standard-2
    if: always()
    needs: [workflow-start, build-ios, build-android]
    steps:
    - name: Output Download Links Summary
      run: |
        echo "### ✅ Success!" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY

        # iOS download link - adapt based on actual results
        if [ "${{ github.event.inputs.platform }}" = "iOS & Android" ] || [ "${{ github.event.inputs.platform }}" = "iOS" ]; then
          if [ "${{ needs.build-ios.result }}" = "success" ] && [ "${{ needs.build-ios.outputs.testflight-upload-success }}" = "true" ]; then
            echo "- **iOS:** Uploaded to TestFlight (${{ needs.workflow-start.outputs.version-code }})" >> $GITHUB_STEP_SUMMARY
          elif [ "${{ needs.build-ios.result }}" = "success" ] && [ "${{ needs.build-ios.outputs.ipa-found }}" = "true" ]; then
            echo "- **iOS:** Build successful, upload to TestFlight failed (${{ needs.workflow-start.outputs.version-code }})" >> $GITHUB_STEP_SUMMARY
          elif [ "${{ needs.build-ios.result }}" = "success" ]; then
            echo "- **iOS:** Build failed, no package generated" >> $GITHUB_STEP_SUMMARY
          else
            echo "- **iOS:** Build failed" >> $GITHUB_STEP_SUMMARY
          fi
        else
          echo "- **iOS:** ⚪ Not requested for this build" >> $GITHUB_STEP_SUMMARY
        fi

        # Android download link - adapt based on actual results
        if [ "${{ github.event.inputs.platform }}" = "iOS & Android" ] || [ "${{ github.event.inputs.platform }}" = "Android" ]; then
          if [ "${{ needs.build-android.result }}" = "success" ]; then
            VERSION_CODE="${{ needs.workflow-start.outputs.android_version_code }}"
            ANDROID_DOWNLOAD_URL="https://play.google.com/apps/test/com.drmaxmuscle.dr_max_muscle/${VERSION_CODE}"
            echo "- **Android:** <a href=\"${ANDROID_DOWNLOAD_URL}\" target=\"_blank\">Direct Download Link</a>" >> $GITHUB_STEP_SUMMARY
          else
            echo "- **Android:** Build failed" >> $GITHUB_STEP_SUMMARY
          fi
        else
          echo "- **Android:** ⚪ Not requested for this build" >> $GITHUB_STEP_SUMMARY
        fi

